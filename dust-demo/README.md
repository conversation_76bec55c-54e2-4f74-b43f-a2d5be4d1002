# Dust-Core 演示项目

这个项目包含了Dust-Core框架的完整演示代码，展示了如何使用这个基于Java 21虚拟线程的高性能Actor框架。

## 项目结构

```
dust-demo/
├── src/main/java/com/mentalresonance/dust/demo/
│   ├── QuickStartExample.java              # 主要的快速入门演示
│   ├── actors/                             # Actor实现
│   │   ├── HelloActor.java                 # 基础Actor演示
│   │   ├── CounterActor.java               # 状态管理演示
│   │   ├── PersistentCounterActor.java     # 持久化Actor演示
│   │   └── ParentActor.java                # 父子Actor管理演示
│   └── examples/                           # 高级示例
│       ├── RequestResponseExample.java     # 请求-响应模式
│       └── ErrorHandlingExample.java       # 错误处理和监督策略
├── src/main/resources/
│   └── logback.xml                         # 日志配置
├── pom.xml                                 # Maven配置
└── README.md                               # 本文件
```

## 快速开始

### 1. 环境要求

- Java 21 或更高版本
- Maven 3.6 或更高版本

### 2. 编译和运行

```bash
# 编译项目
mvn clean compile

# 运行主要演示
mvn exec:java -Dexec.mainClass="com.mentalresonance.dust.demo.QuickStartExample"

# 运行请求-响应演示
mvn exec:java -Dexec.mainClass="com.mentalresonance.dust.demo.examples.RequestResponseExample"

# 运行错误处理演示
mvn exec:java -Dexec.mainClass="com.mentalresonance.dust.demo.examples.ErrorHandlingExample"

# 打包为可执行JAR
mvn clean package

# 运行打包后的JAR
java --enable-preview -jar target/dust-demo-1.0.0.jar
```

## 演示内容

### 1. QuickStartExample - 快速入门演示

这是主要的演示程序，包含：

- **HelloActor**: 展示基本的消息处理和类型匹配
- **CounterActor**: 演示状态管理和命令处理
- **PersistentCounterActor**: 展示状态持久化和恢复
- **ParentActor**: 演示Actor层次结构和子Actor管理

运行后您将看到：
```
=== Dust-Core 快速入门演示开始 ===

1. 创建Actor系统...
2. 设置持久化服务...
3. 演示HelloActor...
HelloActor 'DemoHello' 收到字符串消息: hello
HelloActor 'DemoHello' 收到数字: 42
...
```

### 2. RequestResponseExample - 请求-响应模式

演示如何在Actor之间实现请求-响应通信：

- **CalculatorActor**: 处理数学计算请求
- **ClientActor**: 发送请求并处理响应
- 支持多种请求类型（加法、乘法、除法）
- 错误处理和响应

### 3. ErrorHandlingExample - 错误处理演示

展示Actor的容错机制：

- **SupervisorActor**: 监督Worker并处理异常
- **WorkerActor**: 执行任务并可能抛出异常
- **CustomSupervisionStrategy**: 自定义监督策略
- 不同异常类型的不同处理策略（恢复、重启、停止）

## 核心概念演示

### Actor基础
```java
// 创建Actor系统
ActorSystem system = ActorSystem.create("MySystem");

// 创建Actor
ActorRef actor = system.getContext().actorOf(HelloActor.props("MyActor"), "my-actor");

// 发送消息
actor.tell("Hello, World!", null);
```

### 消息处理
```java
@Override
protected ActorBehavior createBehavior() {
    return message -> {
        switch (message) {
            case String msg -> handleString(msg);
            case Integer num -> handleNumber(num);
            default -> handleUnknown(message);
        }
    };
}
```

### 状态持久化
```java
// 保存状态
saveSnapshot(state);

// 恢复状态
@Override
protected void onRecovery(Serializable recoveredState) {
    this.state = (MyState) recoveredState;
}
```

### 父子Actor管理
```java
// 创建子Actor
ActorRef child = actorOf(ChildActor.props(), "child");

// 监视子Actor
watch(child);

// 转发消息
child.tell(message, sender);
```

## 配置说明

### 持久化配置
演示使用Gson进行JSON格式的状态持久化：
```java
system.getContext().setPersistenceService(
    GsonPersistenceService.create("./demo-data", "json"));
```

### 日志配置
使用Logback进行日志管理，配置文件位于`src/main/resources/logback.xml`。

## 学习路径

1. **开始**: 运行`QuickStartExample`了解基本概念
2. **深入**: 查看各个Actor的实现代码
3. **进阶**: 运行`RequestResponseExample`学习通信模式
4. **高级**: 运行`ErrorHandlingExample`了解容错机制
5. **实践**: 修改代码或创建自己的Actor

## 常见问题

### Q: 如何创建自定义Actor？
A: 继承`Actor`类并实现`createBehavior()`方法，参考`HelloActor`的实现。

### Q: 如何处理异步响应？
A: 使用`sender.tell(response, self)`向发送者回复消息，参考`RequestResponseExample`。

### Q: 如何实现状态持久化？
A: 继承`PersistentActor`并实现`onRecovery()`方法，参考`PersistentCounterActor`。

### Q: 如何处理Actor异常？
A: 实现自定义的`SupervisionStrategy`，参考`ErrorHandlingExample`。

## 扩展建议

1. **添加新的Actor类型**: 创建处理特定业务逻辑的Actor
2. **实现远程通信**: 在不同JVM之间进行Actor通信
3. **集成数据库**: 使用数据库进行状态持久化
4. **添加监控**: 实现Actor系统的监控和指标收集
5. **性能测试**: 创建负载测试来评估系统性能

## 技术支持

如果您在使用过程中遇到问题，请：

1. 查看日志文件 `logs/dust-demo.log`
2. 检查Java版本是否为21或更高
3. 确保启用了预览特性 `--enable-preview`
4. 参考Dust-Core框架的完整文档

## 许可证

本演示项目遵循与Dust-Core框架相同的许可证。
