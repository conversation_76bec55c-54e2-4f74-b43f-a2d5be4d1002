#!/bin/bash

# Dust-Core 演示运行脚本
# 
# 这个脚本提供了运行各种演示的便捷方式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Java版本
check_java() {
    if ! command -v java &> /dev/null; then
        print_error "Java未安装或不在PATH中"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$JAVA_VERSION" -lt 21 ]; then
        print_error "需要Java 21或更高版本，当前版本: $JAVA_VERSION"
        exit 1
    fi
    
    print_success "Java版本检查通过: $JAVA_VERSION"
}

# 检查Maven
check_maven() {
    if ! command -v mvn &> /dev/null; then
        print_error "Maven未安装或不在PATH中"
        exit 1
    fi
    
    print_success "Maven检查通过"
}

# 编译项目
compile_project() {
    print_info "编译项目..."
    if mvn clean compile -q; then
        print_success "项目编译成功"
    else
        print_error "项目编译失败"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    mkdir -p logs
    mkdir -p demo-data
    print_info "创建了必要的目录"
}

# 运行快速入门演示
run_quickstart() {
    print_info "运行快速入门演示..."
    echo "========================================"
    mvn exec:java -Dexec.mainClass="com.mentalresonance.dust.demo.QuickStartExample" -q
    echo "========================================"
    print_success "快速入门演示完成"
}

# 运行请求-响应演示
run_request_response() {
    print_info "运行请求-响应演示..."
    echo "========================================"
    mvn exec:java -Dexec.mainClass="com.mentalresonance.dust.demo.examples.RequestResponseExample" -q
    echo "========================================"
    print_success "请求-响应演示完成"
}

# 运行错误处理演示
run_error_handling() {
    print_info "运行错误处理演示..."
    echo "========================================"
    mvn exec:java -Dexec.mainClass="com.mentalresonance.dust.demo.examples.ErrorHandlingExample" -q
    echo "========================================"
    print_success "错误处理演示完成"
}

# 打包项目
package_project() {
    print_info "打包项目..."
    if mvn clean package -q; then
        print_success "项目打包成功"
        print_info "可执行JAR位置: target/dust-demo-1.0.0.jar"
    else
        print_error "项目打包失败"
        exit 1
    fi
}

# 运行打包后的JAR
run_jar() {
    if [ ! -f "target/dust-demo-1.0.0.jar" ]; then
        print_warning "JAR文件不存在，正在打包..."
        package_project
    fi
    
    print_info "运行打包后的JAR..."
    java --enable-preview -jar target/dust-demo-1.0.0.jar
}

# 清理项目
clean_project() {
    print_info "清理项目..."
    mvn clean -q
    rm -rf logs demo-data
    print_success "项目清理完成"
}

# 显示帮助信息
show_help() {
    echo "Dust-Core 演示运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  quickstart      运行快速入门演示 (默认)"
    echo "  request         运行请求-响应演示"
    echo "  error           运行错误处理演示"
    echo "  all             运行所有演示"
    echo "  package         打包项目为可执行JAR"
    echo "  jar             运行打包后的JAR"
    echo "  clean           清理项目"
    echo "  help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                # 运行快速入门演示"
    echo "  $0 all           # 运行所有演示"
    echo "  $0 package       # 打包项目"
    echo "  $0 clean         # 清理项目"
}

# 运行所有演示
run_all() {
    print_info "运行所有演示..."
    run_quickstart
    echo ""
    run_request_response
    echo ""
    run_error_handling
    print_success "所有演示完成"
}

# 主函数
main() {
    print_info "Dust-Core 演示脚本启动"
    
    # 检查环境
    check_java
    check_maven
    
    # 创建目录
    create_directories
    
    # 编译项目
    compile_project
    
    # 根据参数执行相应操作
    case "${1:-quickstart}" in
        "quickstart")
            run_quickstart
            ;;
        "request")
            run_request_response
            ;;
        "error")
            run_error_handling
            ;;
        "all")
            run_all
            ;;
        "package")
            package_project
            ;;
        "jar")
            run_jar
            ;;
        "clean")
            clean_project
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
    
    print_success "脚本执行完成"
}

# 执行主函数
main "$@"
