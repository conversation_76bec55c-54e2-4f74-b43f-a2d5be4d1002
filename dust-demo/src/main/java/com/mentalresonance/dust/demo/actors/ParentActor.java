package com.mentalresonance.dust.demo.actors;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.msgs.Terminated;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 父Actor演示：展示Actor层次结构和子Actor管理
 * 
 * 这个Actor演示了：
 * 1. 如何创建和管理子Actor
 * 2. 如何监视子Actor的生命周期
 * 3. 如何转发消息给子Actor
 * 4. 父子Actor之间的通信模式
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class ParentActor extends Actor {
    
    /**
     * 子Actor的引用映射
     */
    private final Map<String, ActorRef> childActors = new HashMap<>();
    
    /**
     * 父Actor的名称
     */
    private final String parentName;
    
    /**
     * 创建Props的静态方法
     * 
     * @param parentName 父Actor名称
     * @return Props配置对象
     */
    public static Props props(String parentName) {
        return Props.create(ParentActor.class, parentName);
    }
    
    /**
     * 构造函数
     * 
     * @param parentName 父Actor名称
     */
    public ParentActor(String parentName) {
        this.parentName = parentName;
        log.info("父Actor '{}' 已创建", parentName);
    }
    
    /**
     * Actor启动时创建一些子Actor
     */
    @Override
    protected void preStart() {
        try {
            // 创建Hello子Actor
            ActorRef helloChild = actorOf(HelloActor.props("HelloChild"), "hello-child");
            childActors.put("hello", helloChild);
            watch(helloChild); // 监视子Actor
            
            // 创建Counter子Actor
            ActorRef counterChild = actorOf(CounterActor.props("CounterChild"), "counter-child");
            childActors.put("counter", counterChild);
            watch(counterChild);
            
            log.info("父Actor '{}' 已创建 {} 个子Actor", parentName, childActors.size());
            
        } catch (Exception e) {
            log.error("父Actor '{}' 创建子Actor失败: {}", parentName, e.getMessage());
        }
    }
    
    /**
     * 定义父Actor的消息处理行为
     * 
     * @return ActorBehavior消息处理行为
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                // 处理创建子Actor的命令
                case CreateChildMsg msg -> {
                    createChild(msg.childType, msg.childName);
                }
                
                // 处理停止子Actor的命令
                case StopChildMsg msg -> {
                    stopChild(msg.childName);
                }
                
                // 处理转发消息的命令
                case ForwardMsg msg -> {
                    forwardToChild(msg.childName, msg.message);
                }
                
                // 处理广播消息的命令
                case BroadcastMsg msg -> {
                    broadcastToChildren(msg.message);
                }
                
                // 处理子Actor终止消息
                case Terminated terminated -> {
                    handleChildTerminated(terminated);
                }
                
                // 处理字符串命令
                case String cmd -> {
                    switch (cmd.toLowerCase()) {
                        case "list", "children" -> {
                            listChildren();
                        }
                        
                        case "count" -> {
                            sender.tell(childActors.size(), self);
                        }
                        
                        case "stop-all" -> {
                            stopAllChildren();
                        }
                        
                        default -> {
                            // 检查是否是转发命令格式 "forward:child:message"
                            if (cmd.startsWith("forward:")) {
                                String[] parts = cmd.split(":", 3);
                                if (parts.length == 3) {
                                    forwardToChild(parts[1], parts[2]);
                                } else {
                                    sender.tell("错误：转发命令格式应为 'forward:child:message'", self);
                                }
                            } else {
                                log.warn("父Actor '{}' 收到未知命令: {}", parentName, cmd);
                                sender.tell("错误：未知命令 '" + cmd + "'", self);
                            }
                        }
                    }
                }
                
                // 处理未知消息
                default -> {
                    log.warn("父Actor '{}' 收到未知消息类型: {} - {}", 
                        parentName, message.getClass().getSimpleName(), message);
                    sender.tell("错误：不支持的消息类型", self);
                }
            }
        };
    }
    
    /**
     * 创建子Actor
     */
    private void createChild(String childType, String childName) {
        try {
            if (childActors.containsKey(childName)) {
                log.warn("父Actor '{}' 子Actor '{}' 已存在", parentName, childName);
                sender.tell("错误：子Actor已存在", self);
                return;
            }
            
            ActorRef childRef;
            switch (childType.toLowerCase()) {
                case "hello" -> {
                    childRef = actorOf(HelloActor.props(childName), childName);
                }
                case "counter" -> {
                    childRef = actorOf(CounterActor.props(childName), childName);
                }
                case "persistent-counter" -> {
                    childRef = actorOf(PersistentCounterActor.props(childName), childName);
                }
                default -> {
                    log.warn("父Actor '{}' 未知的子Actor类型: {}", parentName, childType);
                    sender.tell("错误：未知的子Actor类型", self);
                    return;
                }
            }
            
            childActors.put(childName, childRef);
            watch(childRef);
            
            log.info("父Actor '{}' 创建了子Actor '{}' (类型: {})", parentName, childName, childType);
            sender.tell("子Actor创建成功: " + childName, self);
            
        } catch (Exception e) {
            log.error("父Actor '{}' 创建子Actor失败: {}", parentName, e.getMessage());
            sender.tell("错误：创建子Actor失败", self);
        }
    }
    
    /**
     * 停止子Actor
     */
    private void stopChild(String childName) {
        ActorRef childRef = childActors.get(childName);
        if (childRef != null) {
            context.stop(childRef);
            log.info("父Actor '{}' 停止子Actor '{}'", parentName, childName);
            sender.tell("子Actor停止命令已发送: " + childName, self);
        } else {
            log.warn("父Actor '{}' 子Actor '{}' 不存在", parentName, childName);
            sender.tell("错误：子Actor不存在", self);
        }
    }
    
    /**
     * 转发消息给指定子Actor
     */
    private void forwardToChild(String childName, Serializable message) {
        ActorRef childRef = childActors.get(childName);
        if (childRef != null) {
            childRef.tell(message, sender);
            log.info("父Actor '{}' 转发消息给子Actor '{}': {}", parentName, childName, message);
        } else {
            log.warn("父Actor '{}' 子Actor '{}' 不存在，无法转发消息", parentName, childName);
            sender.tell("错误：子Actor不存在，无法转发消息", self);
        }
    }
    
    /**
     * 广播消息给所有子Actor
     */
    private void broadcastToChildren(Serializable message) {
        int count = 0;
        for (Map.Entry<String, ActorRef> entry : childActors.entrySet()) {
            entry.getValue().tell(message, sender);
            count++;
        }
        log.info("父Actor '{}' 广播消息给 {} 个子Actor: {}", parentName, count, message);
        sender.tell("消息已广播给 " + count + " 个子Actor", self);
    }
    
    /**
     * 列出所有子Actor
     */
    private void listChildren() {
        StringBuilder sb = new StringBuilder();
        sb.append("父Actor '").append(parentName).append("' 的子Actor列表:\n");
        
        if (childActors.isEmpty()) {
            sb.append("  (无子Actor)");
        } else {
            for (Map.Entry<String, ActorRef> entry : childActors.entrySet()) {
                sb.append("  - ").append(entry.getKey())
                  .append(" (").append(entry.getValue().path).append(")\n");
            }
        }
        
        log.info(sb.toString());
        sender.tell(sb.toString(), self);
    }
    
    /**
     * 停止所有子Actor
     */
    private void stopAllChildren() {
        int count = childActors.size();
        for (ActorRef childRef : childActors.values()) {
            context.stop(childRef);
        }
        log.info("父Actor '{}' 停止所有 {} 个子Actor", parentName, count);
        sender.tell("所有子Actor停止命令已发送", self);
    }
    
    /**
     * 处理子Actor终止消息
     */
    private void handleChildTerminated(Terminated terminated) {
        String terminatedName = terminated.getName();
        childActors.remove(terminatedName);
        log.info("父Actor '{}' 收到子Actor '{}' 终止通知", parentName, terminatedName);
    }
    
    /**
     * Actor停止时的清理工作
     */
    @Override
    protected void postStop() {
        log.info("父Actor '{}' 已停止，剩余子Actor数量: {}", parentName, childActors.size());
    }
    
    // 消息类定义
    
    /**
     * 创建子Actor消息
     */
    public static class CreateChildMsg implements Serializable {
        public final String childType;
        public final String childName;
        
        public CreateChildMsg(String childType, String childName) {
            this.childType = childType;
            this.childName = childName;
        }
    }
    
    /**
     * 停止子Actor消息
     */
    public static class StopChildMsg implements Serializable {
        public final String childName;
        
        public StopChildMsg(String childName) {
            this.childName = childName;
        }
    }
    
    /**
     * 转发消息
     */
    public static class ForwardMsg implements Serializable {
        public final String childName;
        public final Serializable message;
        
        public ForwardMsg(String childName, Serializable message) {
            this.childName = childName;
            this.message = message;
        }
    }
    
    /**
     * 广播消息
     */
    public static class BroadcastMsg implements Serializable {
        public final Serializable message;
        
        public BroadcastMsg(Serializable message) {
            this.message = message;
        }
    }
}
