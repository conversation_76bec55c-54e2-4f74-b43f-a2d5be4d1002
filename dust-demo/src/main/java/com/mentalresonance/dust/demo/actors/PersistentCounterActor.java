package com.mentalresonance.dust.demo.actors;

import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.PersistentActor;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.msgs.SnapshotSuccessMsg;
import com.mentalresonance.dust.core.msgs.SnapshotFailureMsg;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 持久化计数器Actor演示：展示状态持久化
 * 
 * 这个Actor演示了：
 * 1. 如何使用PersistentActor进行状态持久化
 * 2. 如何处理快照保存和恢复
 * 3. 如何处理持久化成功和失败的消息
 * 4. 状态的自动恢复机制
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class PersistentCounterActor extends PersistentActor {
    
    /**
     * 计数器状态类：需要持久化的状态数据
     */
    @Data
    public static class CounterState implements Serializable {
        /**
         * 计数器名称
         */
        private String name;
        
        /**
         * 当前计数值
         */
        private int count = 0;
        
        /**
         * 创建时间
         */
        private long createdTime;
        
        /**
         * 最后更新时间
         */
        private long lastUpdated;
        
        /**
         * 操作次数
         */
        private int operationCount = 0;
        
        /**
         * 默认构造函数
         */
        public CounterState() {
            this.createdTime = System.currentTimeMillis();
            this.lastUpdated = this.createdTime;
        }
        
        /**
         * 带名称的构造函数
         */
        public CounterState(String name) {
            this();
            this.name = name;
        }
        
        /**
         * 更新状态
         */
        public void updateState() {
            this.lastUpdated = System.currentTimeMillis();
            this.operationCount++;
        }
        
        @Override
        public String toString() {
            return String.format("CounterState{name='%s', count=%d, operations=%d, created=%d, updated=%d}", 
                name, count, operationCount, createdTime, lastUpdated);
        }
    }
    
    /**
     * 计数器状态
     */
    private CounterState state;
    
    /**
     * 创建Props的静态方法
     * 
     * @param counterName 计数器名称
     * @return Props配置对象
     */
    public static Props props(String counterName) {
        return Props.create(PersistentCounterActor.class, counterName);
    }
    
    /**
     * 构造函数
     * 
     * @param counterName 计数器名称
     */
    public PersistentCounterActor(String counterName) {
        this.state = new CounterState(counterName);
        log.info("持久化计数器Actor '{}' 已创建", counterName);
    }
    
    /**
     * 定义持久化计数器的消息处理行为
     * 
     * @return ActorBehavior消息处理行为
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                // 处理字符串命令
                case String cmd -> {
                    switch (cmd.toLowerCase()) {
                        case "increment", "inc", "++" -> {
                            state.setCount(state.getCount() + 1);
                            state.updateState();
                            saveSnapshot(state); // 保存状态快照
                            log.info("持久化计数器 '{}' 增加到: {}", state.getName(), state.getCount());
                            sender.tell(state.getCount(), self);
                        }
                        
                        case "decrement", "dec", "--" -> {
                            state.setCount(state.getCount() - 1);
                            state.updateState();
                            saveSnapshot(state);
                            log.info("持久化计数器 '{}' 减少到: {}", state.getName(), state.getCount());
                            sender.tell(state.getCount(), self);
                        }
                        
                        case "get", "value", "current" -> {
                            log.info("持久化计数器 '{}' 当前值: {}", state.getName(), state.getCount());
                            sender.tell(state.getCount(), self);
                        }
                        
                        case "reset", "clear" -> {
                            int oldValue = state.getCount();
                            state.setCount(0);
                            state.updateState();
                            saveSnapshot(state);
                            log.info("持久化计数器 '{}' 已重置: {} -> {}", 
                                state.getName(), oldValue, state.getCount());
                            sender.tell(state.getCount(), self);
                        }
                        
                        case "status", "info" -> {
                            log.info("持久化计数器 '{}' 状态: {}", state.getName(), state);
                            sender.tell(state, self);
                        }
                        
                        case "save", "snapshot" -> {
                            saveSnapshot(state);
                            log.info("持久化计数器 '{}' 手动保存快照", state.getName());
                            sender.tell("快照保存请求已发送", self);
                        }
                        
                        default -> {
                            // 检查是否是设置值的命令
                            if (cmd.startsWith("set:")) {
                                try {
                                    int newValue = Integer.parseInt(cmd.substring(4));
                                    int oldValue = state.getCount();
                                    state.setCount(newValue);
                                    state.updateState();
                                    saveSnapshot(state);
                                    log.info("持久化计数器 '{}' 设置为: {} -> {}", 
                                        state.getName(), oldValue, state.getCount());
                                    sender.tell(state.getCount(), self);
                                } catch (NumberFormatException e) {
                                    log.warn("持久化计数器 '{}' 无效的设置命令: {}", state.getName(), cmd);
                                    sender.tell("错误：无效的数字格式", self);
                                }
                            } else {
                                log.warn("持久化计数器 '{}' 收到未知命令: {}", state.getName(), cmd);
                                sender.tell("错误：未知命令 '" + cmd + "'", self);
                            }
                        }
                    }
                }
                
                // 处理整数消息
                case Integer newValue -> {
                    int oldValue = state.getCount();
                    state.setCount(newValue);
                    state.updateState();
                    saveSnapshot(state);
                    log.info("持久化计数器 '{}' 直接设置为: {} -> {}", 
                        state.getName(), oldValue, state.getCount());
                    sender.tell(state.getCount(), self);
                }
                
                // 处理快照保存成功消息
                case SnapshotSuccessMsg ignored -> {
                    log.info("持久化计数器 '{}' 快照保存成功", state.getName());
                }
                
                // 处理快照保存失败消息
                case SnapshotFailureMsg msg -> {
                    log.error("持久化计数器 '{}' 快照保存失败: {}", 
                        state.getName(), msg.getException().getMessage());
                }
                
                // 处理未知消息
                default -> {
                    log.warn("持久化计数器 '{}' 收到未知消息类型: {} - {}", 
                        state.getName(), message.getClass().getSimpleName(), message);
                    sender.tell("错误：不支持的消息类型", self);
                }
            }
        };
    }
    
    /**
     * 状态恢复处理：从持久化存储中恢复状态
     * 
     * 当Actor重启时，这个方法会被调用来恢复之前保存的状态。
     * 
     * @param recoveredState 从持久化存储中恢复的状态
     */
   /* @Override
    protected void onRecovery(Serializable recoveredState) {
        if (recoveredState instanceof CounterState counterState) {
            this.state = counterState;
            log.info("持久化计数器 '{}' 状态已恢复: {}", state.getName(), state);
        } else {
            log.warn("持久化计数器恢复了无效的状态类型: {}", 
                recoveredState != null ? recoveredState.getClass() : "null");
        }
    }*/
    
    /**
     * Actor启动后的处理
     */
    @Override
    protected void postRecovery() {
        log.info("持久化计数器 '{}' 恢复完成，当前值: {}", state.getName(), state.getCount());
    }
    
    /**
     * Actor停止时的处理
     */
    @Override
    protected void postStop() {
        log.info("持久化计数器 '{}' 已停止，最终状态: {}", state.getName(), state);
    }
    
    /**
     * Actor重启前的处理
     */
    @Override
    protected void preRestart(Throwable reason) {
        log.warn("持久化计数器 '{}' 即将重启，原因: {}，当前状态: {}", 
            state.getName(), reason.getMessage(), state);
        // 在重启前保存当前状态
        saveSnapshot(state);
    }
}
