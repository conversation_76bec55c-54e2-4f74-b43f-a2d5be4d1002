package com.mentalresonance.dust.demo.ai.agents;

import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.demo.ai.messages.AIMessages.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 协调Agent：负责多Agent系统的协调和任务分发
 * 
 * 核心职责：
 * 1. 任务分解和分发
 * 2. Agent能力匹配
 * 3. 工作流程编排
 * 4. 结果整合和综合
 * 5. 负载均衡和资源调度
 * 6. 协作冲突解决
 * 
 * 协调策略：
 * - 能力匹配：根据任务需求匹配最适合的Agent
 * - 负载均衡：避免单个Agent过载
 * - 并行处理：将复杂任务分解为可并行的子任务
 * - 结果融合：整合多个Agent的输出
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class CoordinatorAgent extends BaseAIAgent {
    
    /**
     * 注册的Agent及其能力
     */
    private final Map<String, AgentInfo> registeredAgents = new ConcurrentHashMap<>();
    
    /**
     * 活跃的协作会话
     */
    private final Map<String, CollaborationSession> activeSessions = new ConcurrentHashMap<>();
    
    /**
     * 任务队列
     */
    private final Queue<TaskRequest> taskQueue = new LinkedList<>();
    
    /**
     * 会话ID生成器
     */
    private final AtomicInteger sessionIdGenerator = new AtomicInteger(1);
    
    /**
     * 创建Props
     */
    public static Props props(String agentName) {
        return Props.create(CoordinatorAgent.class, agentName);
    }
    
    /**
     * 构造函数
     */
    public CoordinatorAgent(String agentName) {
        super(agentName, "CoordinatorAgent");
    }
    
    @Override
    protected void initializeCapabilities() {
        capabilities.add("任务分解");
        capabilities.add("Agent匹配");
        capabilities.add("工作流编排");
        capabilities.add("结果整合");
        capabilities.add("负载均衡");
        capabilities.add("冲突解决");
        capabilities.add("资源调度");
        capabilities.add("协作管理");
    }
    
    @Override
    protected void onAgentStart() {
        // 启动时注册已知的Agent（在实际应用中可能从配置文件读取）
        log.info("协调Agent启动，开始发现其他Agent...");
        
        // 定期检查Agent状态
        scheduleIn("check-agents", 10000L);
    }
    
    @Override
    protected void processThinkingRequest(ThinkingRequest request) {
        log.info("协调复杂思考任务: {}", request.getQuery());
        
        // 创建协作会话
        String sessionId = "session-" + sessionIdGenerator.getAndIncrement();
        CollaborationSession session = new CollaborationSession(sessionId, request);
        activeSessions.put(sessionId, session);
        
        // 分析任务复杂度和所需能力
        TaskAnalysis analysis = analyzeTask(request);
        
        // 根据分析结果选择协作策略
        if (analysis.complexity > 0.7) {
            // 高复杂度任务：多Agent协作
            orchestrateMultiAgentThinking(session, analysis);
        } else if (analysis.complexity > 0.4) {
            // 中等复杂度：专家Agent处理
            delegateToExpertAgent(session, analysis);
        } else {
            // 简单任务：直接处理
            handleSimpleTask(session);
        }
    }
    
    /**
     * 分析任务
     */
    private TaskAnalysis analyzeTask(ThinkingRequest request) {
        TaskAnalysis analysis = new TaskAnalysis();
        
        String query = request.getQuery().toLowerCase();
        
        // 计算复杂度
        analysis.complexity = calculateComplexity(query);
        
        // 识别所需能力
        if (query.contains("分析") || query.contains("推理") || query.contains("逻辑")) {
            analysis.requiredCapabilities.add("深度推理");
        }
        if (query.contains("知识") || query.contains("信息") || query.contains("查询")) {
            analysis.requiredCapabilities.add("知识管理");
        }
        if (query.contains("创新") || query.contains("创造") || query.contains("设计")) {
            analysis.requiredCapabilities.add("创造性思考");
        }
        if (query.contains("学习") || query.contains("训练") || query.contains("改进")) {
            analysis.requiredCapabilities.add("机器学习");
        }
        
        // 估算处理时间
        analysis.estimatedTime = (long) (analysis.complexity * 30000); // 基础30秒
        
        log.info("任务分析完成 - 复杂度: {:.2f}, 所需能力: {}, 预估时间: {}ms", 
            analysis.complexity, analysis.requiredCapabilities, analysis.estimatedTime);
        
        return analysis;
    }
    
    /**
     * 计算任务复杂度
     */
    private double calculateComplexity(String query) {
        double complexity = 0.3; // 基础复杂度
        
        // 基于关键词增加复杂度
        String[] complexKeywords = {"分析", "推理", "比较", "评估", "综合", "创新", "设计"};
        for (String keyword : complexKeywords) {
            if (query.contains(keyword)) complexity += 0.1;
        }
        
        // 基于长度增加复杂度
        if (query.length() > 100) complexity += 0.2;
        if (query.length() > 200) complexity += 0.2;
        
        // 基于问号数量（多个问题）
        long questionCount = query.chars().filter(ch -> ch == '?').count();
        complexity += questionCount * 0.1;
        
        return Math.min(1.0, complexity);
    }
    
    /**
     * 编排多Agent思考
     */
    private void orchestrateMultiAgentThinking(CollaborationSession session, TaskAnalysis analysis) {
        log.info("启动多Agent协作思考，会话ID: {}", session.sessionId);
        
        // 选择参与的Agent
        List<AgentInfo> selectedAgents = selectAgentsForTask(analysis);
        session.participatingAgents.addAll(selectedAgents);
        
        // 分解任务
        List<SubTask> subTasks = decomposeTask(session.originalRequest, selectedAgents);
        session.subTasks.addAll(subTasks);
        
        // 分发子任务
        for (SubTask subTask : subTasks) {
            AgentInfo agent = findBestAgentForSubTask(subTask, selectedAgents);
            if (agent != null) {
                CollaborationRequest colabRequest = new CollaborationRequest(
                    agentName, agent.name, subTask.description, CollaborationType.DELEGATION);
                colabRequest.setSessionId(session.sessionId);
                
                agent.ref.tell(colabRequest, self);
                session.pendingResponses.add(subTask.id);
                
                log.info("分发子任务 '{}' 给Agent '{}'", subTask.description, agent.name);
            }
        }
        
        // 设置超时处理
        scheduleIn(new SessionTimeout(session.sessionId), analysis.estimatedTime + 10000L);
    }
    
    /**
     * 委托给专家Agent
     */
    private void delegateToExpertAgent(CollaborationSession session, TaskAnalysis analysis) {
        log.info("委托给专家Agent处理");
        
        AgentInfo expertAgent = findExpertAgent(analysis.requiredCapabilities);
        if (expertAgent != null) {
            session.participatingAgents.add(expertAgent);
            
            CollaborationRequest request = new CollaborationRequest(
                agentName, expertAgent.name, session.originalRequest.getQuery(), 
                CollaborationType.DELEGATION);
            request.setSessionId(session.sessionId);
            
            expertAgent.ref.tell(request, self);
            session.pendingResponses.add("expert-response");
            
            log.info("任务委托给专家Agent: {}", expertAgent.name);
        } else {
            // 没有找到合适的专家，回退到简单处理
            handleSimpleTask(session);
        }
    }
    
    /**
     * 处理简单任务
     */
    private void handleSimpleTask(CollaborationSession session) {
        log.info("直接处理简单任务");
        
        // 生成简单响应
        List<ReasoningStep> steps = Arrays.asList(
            new ReasoningStep(1, "任务分析", "分析任务复杂度", 
                session.originalRequest.getQuery(), "确定为简单任务，直接处理", 0.8, 100, null),
            new ReasoningStep(2, "直接回答", "基于常识和经验回答", 
                "简单推理", "生成了基础回答", 0.7, 200, null)
        );
        
        String answer = "基于初步分析，这是一个相对简单的问题。" + 
                       "建议进一步明确需求或提供更多上下文信息以获得更准确的答案。";
        
        ThinkingResponse response = new ThinkingResponse(
            session.originalRequest.getQuery(), answer, steps, 0.6);
        
        // 发送响应
        ActorRef originalSender = session.originalSender;
        if (originalSender != null) {
            originalSender.tell(response, self);
        }
        
        // 清理会话
        activeSessions.remove(session.sessionId);
    }
    
    /**
     * 选择任务所需的Agent
     */
    private List<AgentInfo> selectAgentsForTask(TaskAnalysis analysis) {
        List<AgentInfo> selected = new ArrayList<>();
        
        for (String capability : analysis.requiredCapabilities) {
            AgentInfo agent = findAgentWithCapability(capability);
            if (agent != null && !selected.contains(agent)) {
                selected.add(agent);
            }
        }
        
        // 如果没有找到特定能力的Agent，选择通用Agent
        if (selected.isEmpty()) {
            selected.addAll(registeredAgents.values());
        }
        
        return selected.subList(0, Math.min(3, selected.size())); // 最多3个Agent协作
    }
    
    /**
     * 分解任务
     */
    private List<SubTask> decomposeTask(ThinkingRequest request, List<AgentInfo> agents) {
        List<SubTask> subTasks = new ArrayList<>();
        
        // 简化的任务分解逻辑
        subTasks.add(new SubTask("subtask-1", "信息收集", 
            "收集与问题相关的背景信息和知识"));
        subTasks.add(new SubTask("subtask-2", "深度分析", 
            "对问题进行深入的逻辑分析和推理"));
        subTasks.add(new SubTask("subtask-3", "方案生成", 
            "基于分析结果生成可能的解决方案"));
        
        return subTasks;
    }
    
    /**
     * 为子任务找到最佳Agent
     */
    private AgentInfo findBestAgentForSubTask(SubTask subTask, List<AgentInfo> candidates) {
        // 简化的匹配逻辑
        for (AgentInfo agent : candidates) {
            if (subTask.description.contains("信息") && agent.capabilities.contains("知识管理")) {
                return agent;
            }
            if (subTask.description.contains("分析") && agent.capabilities.contains("深度推理")) {
                return agent;
            }
            if (subTask.description.contains("方案") && agent.capabilities.contains("创造性思考")) {
                return agent;
            }
        }
        
        // 如果没有完美匹配，返回第一个可用的
        return candidates.isEmpty() ? null : candidates.get(0);
    }
    
    /**
     * 找到专家Agent
     */
    private AgentInfo findExpertAgent(Set<String> requiredCapabilities) {
        AgentInfo bestMatch = null;
        int maxMatches = 0;
        
        for (AgentInfo agent : registeredAgents.values()) {
            int matches = 0;
            for (String capability : requiredCapabilities) {
                if (agent.capabilities.contains(capability)) {
                    matches++;
                }
            }
            
            if (matches > maxMatches) {
                maxMatches = matches;
                bestMatch = agent;
            }
        }
        
        return bestMatch;
    }
    
    /**
     * 找到具有特定能力的Agent
     */
    private AgentInfo findAgentWithCapability(String capability) {
        return registeredAgents.values().stream()
            .filter(agent -> agent.capabilities.contains(capability))
            .findFirst()
            .orElse(null);
    }
    
    @Override
    protected void handleCollaborationResponse(CollaborationResponse response) {
        log.info("收到协作响应: {}", response.getRespondingAgent());
        
        String sessionId = response.getSessionId();
        CollaborationSession session = activeSessions.get(sessionId);
        
        if (session != null) {
            session.responses.add(response);
            session.pendingResponses.remove(response.getOriginalRequestId());
            
            // 检查是否所有响应都已收到
            if (session.pendingResponses.isEmpty()) {
                synthesizeAndRespond(session);
            }
        }
    }
    
    /**
     * 综合响应并回复
     */
    private void synthesizeAndRespond(CollaborationSession session) {
        log.info("综合多Agent响应，会话ID: {}", session.sessionId);
        
        // 收集所有响应
        StringBuilder finalAnswer = new StringBuilder();
        finalAnswer.append("基于多Agent协作分析，综合结论如下：\n\n");
        
        List<ReasoningStep> allSteps = new ArrayList<>();
        double totalConfidence = 0.0;
        int responseCount = 0;
        
        for (CollaborationResponse response : session.responses) {
            if (response.isAccepted() && response.getResult() != null) {
                finalAnswer.append("• ").append(response.getRespondingAgent())
                    .append(": ").append(response.getResult()).append("\n");
                
                // 添加推理步骤（如果有的话）
                allSteps.add(new ReasoningStep(allSteps.size() + 1, "协作响应",
                    response.getRespondingAgent() + "的分析", 
                    session.originalRequest.getQuery(),
                    response.getResult(), 0.8, 1000, null));
                
                responseCount++;
            }
        }
        
        if (responseCount == 0) {
            finalAnswer.append("抱歉，协作Agent未能提供有效响应。");
            totalConfidence = 0.1;
        } else {
            finalAnswer.append("\n综合以上分析，我们得出了全面的结论。");
            totalConfidence = Math.min(0.9, 0.6 + responseCount * 0.1);
        }
        
        // 创建最终响应
        ThinkingResponse finalResponse = new ThinkingResponse(
            session.originalRequest.getQuery(),
            finalAnswer.toString(),
            allSteps,
            totalConfidence
        );
        
        // 发送给原始请求者
        if (session.originalSender != null) {
            session.originalSender.tell(finalResponse, self);
        }
        
        // 清理会话
        activeSessions.remove(session.sessionId);
        log.info("协作会话完成: {}", session.sessionId);
    }
    
    @Override
    protected boolean canHandleCollaboration(CollaborationRequest request) {
        return true; // 协调Agent可以处理所有类型的协作请求
    }
    
    @Override
    protected void processCollaborationRequest(CollaborationRequest request) {
        log.info("处理协作请求: {}", request.getTask());
        
        // 将协作请求转换为思考请求
        ThinkingRequest thinkingRequest = new ThinkingRequest(
            request.getTask(), "协作请求", ThinkingMode.COLLABORATIVE);
        thinkingRequest.setSessionId(request.getSessionId());
        
        processThinkingRequest(thinkingRequest);
    }
    
    @Override
    protected void handleStringCommand(String cmd) {
        switch (cmd.toLowerCase()) {
            case "check-agents" -> discoverAgents();
            case "list-agents" -> listRegisteredAgents();
            case "active-sessions" -> listActiveSessions();
            default -> super.handleStringCommand(cmd);
        }
    }
    
    /**
     * 发现其他Agent
     */
    private void discoverAgents() {
        // 在实际应用中，这里会通过服务发现机制找到其他Agent
        // 这里简化为手动注册
        log.info("发现Agent服务...");
        
        // 模拟发现过程
        try {
            // 尝试查找深度思考Agent
            ActorRef thinkingAgent = actorSelection("/user/DeepThinkingAgent").getRef();
            if (thinkingAgent != null) {
                registerAgent("DeepThinkingAgent", thinkingAgent, 
                    Set.of("深度推理", "批判性思维", "创造性思考"));
            }
        } catch (Exception e) {
            log.debug("未找到DeepThinkingAgent");
        }
        
        try {
            // 尝试查找知识管理Agent
            ActorRef knowledgeAgent = actorSelection("/user/KnowledgeManagerAgent").getRef();
            if (knowledgeAgent != null) {
                registerAgent("KnowledgeManagerAgent", knowledgeAgent,
                    Set.of("知识管理", "信息检索", "语义匹配"));
            }
        } catch (Exception e) {
            log.debug("未找到KnowledgeManagerAgent");
        }
        
        log.info("Agent发现完成，已注册 {} 个Agent", registeredAgents.size());
    }
    
    /**
     * 注册Agent
     */
    private void registerAgent(String name, ActorRef ref, Set<String> capabilities) {
        AgentInfo info = new AgentInfo(name, ref, capabilities);
        registeredAgents.put(name, info);
        log.info("注册Agent: {} - 能力: {}", name, capabilities);
    }
    
    /**
     * 列出已注册的Agent
     */
    private void listRegisteredAgents() {
        log.info("已注册的Agent ({} 个):", registeredAgents.size());
        for (AgentInfo agent : registeredAgents.values()) {
            log.info("  - {}: {}", agent.name, agent.capabilities);
        }
    }
    
    /**
     * 列出活跃会话
     */
    private void listActiveSessions() {
        log.info("活跃协作会话 ({} 个):", activeSessions.size());
        for (CollaborationSession session : activeSessions.values()) {
            log.info("  - {}: {} 个参与Agent, {} 个待响应", 
                session.sessionId, session.participatingAgents.size(), 
                session.pendingResponses.size());
        }
    }
    
    // ==================== 内部类定义 ====================
    
    /**
     * Agent信息
     */
    private static class AgentInfo {
        final String name;
        final ActorRef ref;
        final Set<String> capabilities;
        LocalDateTime lastSeen = LocalDateTime.now();
        
        AgentInfo(String name, ActorRef ref, Set<String> capabilities) {
            this.name = name;
            this.ref = ref;
            this.capabilities = new HashSet<>(capabilities);
        }
    }
    
    /**
     * 任务分析结果
     */
    private static class TaskAnalysis {
        double complexity = 0.0;
        Set<String> requiredCapabilities = new HashSet<>();
        long estimatedTime = 5000L;
    }
    
    /**
     * 子任务
     */
    private static class SubTask {
        final String id;
        final String type;
        final String description;
        
        SubTask(String id, String type, String description) {
            this.id = id;
            this.type = type;
            this.description = description;
        }
    }
    
    /**
     * 协作会话
     */
    private static class CollaborationSession {
        final String sessionId;
        final ThinkingRequest originalRequest;
        final LocalDateTime startTime = LocalDateTime.now();
        ActorRef originalSender;
        
        final List<AgentInfo> participatingAgents = new ArrayList<>();
        final List<SubTask> subTasks = new ArrayList<>();
        final List<CollaborationResponse> responses = new ArrayList<>();
        final Set<String> pendingResponses = new HashSet<>();
        
        CollaborationSession(String sessionId, ThinkingRequest request) {
            this.sessionId = sessionId;
            this.originalRequest = request;
        }
    }
    
    /**
     * 会话超时消息
     */
    private static class SessionTimeout implements java.io.Serializable {
        final String sessionId;
        
        SessionTimeout(String sessionId) {
            this.sessionId = sessionId;
        }
    }
    
    /**
     * 任务请求
     */
    private static class TaskRequest {
        final String taskId;
        final ThinkingRequest request;
        final LocalDateTime submitTime = LocalDateTime.now();
        
        TaskRequest(String taskId, ThinkingRequest request) {
            this.taskId = taskId;
            this.request = request;
        }
    }
}
