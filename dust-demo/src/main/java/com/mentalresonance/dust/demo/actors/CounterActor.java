package com.mentalresonance.dust.demo.actors;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.Props;
import lombok.extern.slf4j.Slf4j;

/**
 * 计数器Actor演示：展示状态管理
 * 
 * 这个Actor演示了：
 * 1. 如何在Actor中维护状态
 * 2. 如何处理命令式消息
 * 3. 如何实现简单的业务逻辑
 * 4. 线程安全的状态管理
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class CounterActor extends Actor {
    
    /**
     * 计数器的当前值
     * 由于Actor是单线程处理消息的，所以这个状态是线程安全的
     */
    private int count = 0;
    
    /**
     * 计数器的名称
     */
    private final String counterName;
    
    /**
     * 创建Props的静态方法
     * 
     * @param counterName 计数器名称
     * @return Props配置对象
     */
    public static Props props(String counterName) {
        return Props.create(CounterActor.class, counterName);
    }
    
    /**
     * 默认构造函数，使用默认名称
     */
    public static Props props() {
        return Props.create(CounterActor.class, "DefaultCounter");
    }
    
    /**
     * 构造函数
     * 
     * @param counterName 计数器名称
     */
    public CounterActor(String counterName) {
        this.counterName = counterName;
        log.info("计数器Actor '{}' 已创建，初始值: {}", counterName, count);
    }
    
    /**
     * 默认构造函数
     */
    public CounterActor() {
        this("DefaultCounter");
    }
    
    /**
     * 定义计数器的消息处理行为
     * 
     * 支持的命令：
     * - "increment" 或 "inc": 增加计数器
     * - "decrement" 或 "dec": 减少计数器
     * - "get": 获取当前值
     * - "reset": 重置计数器
     * - "set:<number>": 设置特定值
     * - Integer: 直接设置数值
     * 
     * @return ActorBehavior消息处理行为
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                // 处理字符串命令
                case String cmd -> {
                    switch (cmd.toLowerCase()) {
                        case "increment", "inc", "++" -> {
                            count++;
                            log.info("计数器 '{}' 增加到: {}", counterName, count);
                            sender.tell(count, self);
                        }
                        
                        case "decrement", "dec", "--" -> {
                            count--;
                            log.info("计数器 '{}' 减少到: {}", counterName, count);
                            sender.tell(count, self);
                        }
                        
                        case "get", "value", "current" -> {
                            log.info("计数器 '{}' 当前值: {}", counterName, count);
                            sender.tell(count, self);
                        }
                        
                        case "reset", "clear" -> {
                            int oldValue = count;
                            count = 0;
                            log.info("计数器 '{}' 已重置: {} -> {}", counterName, oldValue, count);
                            sender.tell(count, self);
                        }
                        
                        case "double" -> {
                            count *= 2;
                            log.info("计数器 '{}' 翻倍到: {}", counterName, count);
                            sender.tell(count, self);
                        }
                        
                        case "half" -> {
                            count /= 2;
                            log.info("计数器 '{}' 减半到: {}", counterName, count);
                            sender.tell(count, self);
                        }
                        
                        default -> {
                            // 检查是否是设置值的命令 "set:123"
                            if (cmd.startsWith("set:")) {
                                try {
                                    int newValue = Integer.parseInt(cmd.substring(4));
                                    int oldValue = count;
                                    count = newValue;
                                    log.info("计数器 '{}' 设置为: {} -> {}", counterName, oldValue, count);
                                    sender.tell(count, self);
                                } catch (NumberFormatException e) {
                                    log.warn("计数器 '{}' 无效的设置命令: {}", counterName, cmd);
                                    sender.tell("错误：无效的数字格式", self);
                                }
                            } else {
                                log.warn("计数器 '{}' 收到未知命令: {}", counterName, cmd);
                                sender.tell("错误：未知命令 '" + cmd + "'", self);
                            }
                        }
                    }
                }
                
                // 处理整数消息（直接设置值）
                case Integer newValue -> {
                    int oldValue = count;
                    count = newValue;
                    log.info("计数器 '{}' 直接设置为: {} -> {}", counterName, oldValue, count);
                    sender.tell(count, self);
                }
                
                // 处理增量消息（自定义消息类型）
                case IncrementMsg msg -> {
                    count += msg.amount;
                    log.info("计数器 '{}' 增加 {}: {}", counterName, msg.amount, count);
                    sender.tell(count, self);
                }
                
                // 处理查询消息
                case QueryMsg ignored -> {
                    CounterStatus status = new CounterStatus(counterName, count, 
                        System.currentTimeMillis());
                    log.info("计数器 '{}' 状态查询: {}", counterName, status);
                    sender.tell(status, self);
                }
                
                // 处理未知消息
                default -> {
                    log.warn("计数器 '{}' 收到未知消息类型: {} - {}", 
                        counterName, message.getClass().getSimpleName(), message);
                    sender.tell("错误：不支持的消息类型", self);
                }
            }
        };
    }
    
    /**
     * 增量消息：用于指定增加的数量
     */
    public static class IncrementMsg implements java.io.Serializable {
        public final int amount;
        
        public IncrementMsg(int amount) {
            this.amount = amount;
        }
        
        @Override
        public String toString() {
            return "IncrementMsg{amount=" + amount + "}";
        }
    }
    
    /**
     * 查询消息：用于获取计数器状态
     */
    public static class QueryMsg implements java.io.Serializable {
        public static final QueryMsg INSTANCE = new QueryMsg();
        
        private QueryMsg() {}
        
        @Override
        public String toString() {
            return "QueryMsg{}";
        }
    }
    
    /**
     * 计数器状态：包含计数器的完整状态信息
     */
    public static class CounterStatus implements java.io.Serializable {
        public final String name;
        public final int value;
        public final long timestamp;
        
        public CounterStatus(String name, int value, long timestamp) {
            this.name = name;
            this.value = value;
            this.timestamp = timestamp;
        }
        
        @Override
        public String toString() {
            return String.format("CounterStatus{name='%s', value=%d, timestamp=%d}", 
                name, value, timestamp);
        }
    }
    
    /**
     * Actor停止时的清理工作
     */
    @Override
    protected void postStop() {
        log.info("计数器Actor '{}' 已停止，最终值: {}", counterName, count);
    }
}
