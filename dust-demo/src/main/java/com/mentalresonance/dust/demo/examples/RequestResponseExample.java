package com.mentalresonance.dust.demo.examples;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.ActorSystem;
import com.mentalresonance.dust.core.actors.Props;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 请求-响应模式演示
 * 
 * 这个例子演示了如何在Actor之间实现请求-响应模式，
 * 包括同步和异步的处理方式。
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class RequestResponseExample {
    
    public static void main(String[] args) {
        log.info("=== 请求-响应模式演示开始 ===");
        
        try {
            ActorSystem system = new ActorSystem("RequestResponseSystem");
            
            // 创建服务Actor
            ActorRef calculatorActor = system.getContext().actorOf(
                CalculatorActor.props(), "calculator");
            
            ActorRef clientActor = system.getContext().actorOf(
                ClientActor.props(calculatorActor), "client");
            
            // 启动客户端请求
            clientActor.tell("start", null);
            
            // 等待处理完成
            Thread.sleep(5000);
            
            system.stop();
            log.info("=== 请求-响应模式演示结束 ===");
            
        } catch (Exception e) {
            log.error("演示过程中发生错误", e);
        }
    }
    
    /**
     * 计算器Actor：处理数学计算请求
     */
    @Slf4j
    public static class CalculatorActor extends Actor {
        
        public static Props props() {
            return Props.create(CalculatorActor.class);
        }
        
        @Override
        protected ActorBehavior createBehavior() {
            return message -> {
                switch (message) {
                    case AddRequest req -> {
                        log.info("计算器收到加法请求: {} + {}", req.a, req.b);
                        int result = req.a + req.b;
                        
                        // 模拟计算时间
                        Thread.sleep(100);
                        
                        sender.tell(new CalculationResponse(req.requestId, result), self);
                        log.info("计算器返回结果: {}", result);
                    }
                    
                    case MultiplyRequest req -> {
                        log.info("计算器收到乘法请求: {} * {}", req.a, req.b);
                        int result = req.a * req.b;
                        
                        Thread.sleep(150);
                        
                        sender.tell(new CalculationResponse(req.requestId, result), self);
                        log.info("计算器返回结果: {}", result);
                    }
                    
                    case DivideRequest req -> {
                        log.info("计算器收到除法请求: {} / {}", req.a, req.b);
                        
                        if (req.b == 0) {
                            sender.tell(new ErrorResponse(req.requestId, "除数不能为零"), self);
                        } else {
                            double result = (double) req.a / req.b;
                            Thread.sleep(200);
                            sender.tell(new CalculationResponse(req.requestId, result), self);
                            log.info("计算器返回结果: {}", result);
                        }
                    }
                    
                    default -> {
                        log.warn("计算器收到未知请求: {}", message);
                        sender.tell(new ErrorResponse("unknown", "未知请求类型"), self);
                    }
                }
            };
        }
    }
    
    /**
     * 客户端Actor：发送请求并处理响应
     */
    @Slf4j
    public static class ClientActor extends Actor {
        
        private final ActorRef calculatorRef;
        private int requestCounter = 0;
        
        public static Props props(ActorRef calculatorRef) {
            return Props.create(ClientActor.class, calculatorRef);
        }
        
        public ClientActor(ActorRef calculatorRef) {
            this.calculatorRef = calculatorRef;
        }
        
        @Override
        protected ActorBehavior createBehavior() {
            return message -> {
                switch (message) {
                    case String cmd when "start".equals(cmd) -> {
                        log.info("客户端开始发送请求...");
                        sendRequests();
                    }
                    
                    case CalculationResponse response -> {
                        log.info("客户端收到计算结果 [{}]: {}", 
                            response.requestId, response.result);
                    }
                    
                    case ErrorResponse error -> {
                        log.error("客户端收到错误响应 [{}]: {}", 
                            error.requestId, error.errorMessage);
                    }
                    
                    default -> {
                        log.warn("客户端收到未知消息: {}", message);
                    }
                }
            };
        }
        
        private void sendRequests() {
            // 发送多个不同类型的请求
            calculatorRef.tell(new AddRequest(generateRequestId(), 10, 20), self);
            calculatorRef.tell(new MultiplyRequest(generateRequestId(), 5, 6), self);
            calculatorRef.tell(new DivideRequest(generateRequestId(), 100, 4), self);
            calculatorRef.tell(new DivideRequest(generateRequestId(), 10, 0), self); // 错误情况
            calculatorRef.tell(new AddRequest(generateRequestId(), -5, 15), self);
        }
        
        private String generateRequestId() {
            return "req-" + (++requestCounter);
        }
    }
    
    // 请求消息类
    
    public static abstract class CalculationRequest implements Serializable {
        public final String requestId;
        public final int a;
        public final int b;
        
        public CalculationRequest(String requestId, int a, int b) {
            this.requestId = requestId;
            this.a = a;
            this.b = b;
        }
    }
    
    public static class AddRequest extends CalculationRequest {
        public AddRequest(String requestId, int a, int b) {
            super(requestId, a, b);
        }
    }
    
    public static class MultiplyRequest extends CalculationRequest {
        public MultiplyRequest(String requestId, int a, int b) {
            super(requestId, a, b);
        }
    }
    
    public static class DivideRequest extends CalculationRequest {
        public DivideRequest(String requestId, int a, int b) {
            super(requestId, a, b);
        }
    }
    
    // 响应消息类
    
    public static class CalculationResponse implements Serializable {
        public final String requestId;
        public final Object result;
        
        public CalculationResponse(String requestId, Object result) {
            this.requestId = requestId;
            this.result = result;
        }
    }
    
    public static class ErrorResponse implements Serializable {
        public final String requestId;
        public final String errorMessage;
        
        public ErrorResponse(String requestId, String errorMessage) {
            this.requestId = requestId;
            this.errorMessage = errorMessage;
        }
    }
}
