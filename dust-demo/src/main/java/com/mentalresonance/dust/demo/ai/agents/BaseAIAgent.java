package com.mentalresonance.dust.demo.ai.agents;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.demo.ai.messages.AIMessages.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * AI Agent基类：所有AI Agent的基础实现
 * 
 * 提供了AI Agent的核心功能：
 * 1. 状态管理和监控
 * 2. 消息处理框架
 * 3. 协作机制
 * 4. 性能指标收集
 * 5. 错误处理和恢复
 * 
 * <AUTHOR> Team
 */
@Slf4j
public abstract class BaseAIAgent extends Actor {
    
    // ==================== Agent状态 ====================
    
    /**
     * Agent名称
     */
    @Getter
    protected final String agentName;
    
    /**
     * Agent类型
     */
    @Getter
    protected final String agentType;
    
    /**
     * 当前状态
     */
    @Getter
    protected volatile AgentState currentState = AgentState.IDLE;
    
    /**
     * Agent能力列表
     */
    @Getter
    protected final Set<String> capabilities = new HashSet<>();
    
    /**
     * 活跃任务映射
     */
    protected final Map<String, TaskContext> activeTasks = new ConcurrentHashMap<>();
    
    /**
     * 协作伙伴引用
     */
    protected final Map<String, ActorRef> collaborators = new ConcurrentHashMap<>();
    
    // ==================== 性能指标 ====================
    
    /**
     * 总处理消息数
     */
    protected final AtomicLong totalProcessedMessages = new AtomicLong(0);
    
    /**
     * 总响应时间（毫秒）
     */
    protected final AtomicLong totalResponseTime = new AtomicLong(0);
    
    /**
     * 错误计数
     */
    protected final AtomicLong errorCount = new AtomicLong(0);
    
    /**
     * 启动时间
     */
    protected final LocalDateTime startTime = LocalDateTime.now();
    
    /**
     * 最后活跃时间
     */
    protected volatile LocalDateTime lastActiveTime = LocalDateTime.now();
    
    // ==================== 构造函数 ====================
    
    /**
     * 构造函数
     * 
     * @param agentName Agent名称
     * @param agentType Agent类型
     */
    protected BaseAIAgent(String agentName, String agentType) {
        this.agentName = agentName;
        this.agentType = agentType;
        initializeCapabilities();
    }
    
    /**
     * 初始化Agent能力
     * 子类应该重写此方法来定义自己的能力
     */
    protected abstract void initializeCapabilities();
    
    // ==================== Actor生命周期 ====================
    
    @Override
    protected void preStart() {
        log.info("AI Agent '{}' ({}) 正在启动...", agentName, agentType);
        currentState = AgentState.IDLE;
        onAgentStart();
    }
    
    @Override
    protected void postStop() {
        log.info("AI Agent '{}' ({}) 已停止", agentName, agentType);
        onAgentStop();
    }
    
    /**
     * Agent启动时的自定义处理
     */
    protected void onAgentStart() {
        // 子类可以重写
    }
    
    /**
     * Agent停止时的自定义处理
     */
    protected void onAgentStop() {
        // 子类可以重写
    }
    
    // ==================== 消息处理框架 ====================
    
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            long startTime = System.currentTimeMillis();
            lastActiveTime = LocalDateTime.now();
            
            try {
                totalProcessedMessages.incrementAndGet();
                
                switch (message) {
                    // 思考相关消息
                    case ThinkingRequest request -> handleThinkingRequest(request);
                    case ThinkingResponse response -> handleThinkingResponse(response);
                    
                    // 知识管理消息
                    case KnowledgeQuery query -> handleKnowledgeQuery(query);
                    case KnowledgeResponse response -> handleKnowledgeResponse(response);
                    case KnowledgeUpdate update -> handleKnowledgeUpdate(update);
                    
                    // 协作消息
                    case CollaborationRequest request -> handleCollaborationRequest(request);
                    case CollaborationResponse response -> handleCollaborationResponse(response);
                    
                    // 状态查询
                    case AgentStatusQuery query -> handleStatusQuery(query);
                    
                    // 学习消息
                    case LearningRequest request -> handleLearningRequest(request);
                    case LearningProgress progress -> handleLearningProgress(progress);
                    
                    // 字符串命令
                    case String cmd -> handleStringCommand(cmd);
                    
                    // 自定义消息处理
                    default -> handleCustomMessage(message);
                }
                
            } catch (Exception e) {
                errorCount.incrementAndGet();
                log.error("Agent '{}' 处理消息时发生错误: {}", agentName, e.getMessage(), e);
                handleError(message, e);
            } finally {
                long duration = System.currentTimeMillis() - startTime;
                totalResponseTime.addAndGet(duration);
            }
        };
    }
    
    // ==================== 消息处理方法 ====================
    
    /**
     * 处理思考请求
     */
    protected void handleThinkingRequest(ThinkingRequest request) {
        log.info("Agent '{}' 收到思考请求: {}", agentName, request.getQuery());
        currentState = AgentState.THINKING;
        
        // 创建任务上下文
        TaskContext taskContext = new TaskContext(request.getMessageId(), 
            "THINKING", request.getQuery());
        activeTasks.put(request.getMessageId(), taskContext);
        
        // 委托给子类实现
        processThinkingRequest(request);
    }
    
    /**
     * 处理协作请求
     */
    protected void handleCollaborationRequest(CollaborationRequest request) {
        log.info("Agent '{}' 收到协作请求: {} -> {}", 
            agentName, request.getRequestingAgent(), request.getTask());
        
        currentState = AgentState.COLLABORATING;
        
        // 检查是否能处理此协作
        boolean canCollaborate = canHandleCollaboration(request);
        
        CollaborationResponse response = new CollaborationResponse(
            request.getMessageId(),
            agentName,
            canCollaborate,
            canCollaborate ? "协作请求已接受" : "无法处理此协作请求"
        );
        
        sender.tell(response, self);
        
        if (canCollaborate) {
            processCollaborationRequest(request);
        }
    }
    
    /**
     * 处理状态查询
     */
    protected void handleStatusQuery(AgentStatusQuery query) {
        AgentStatusResponse response = new AgentStatusResponse(agentName, currentState);
        response.setActiveTaskCount(activeTasks.size());
        response.setTotalProcessedMessages(totalProcessedMessages.get());
        response.setAvgResponseTime(calculateAverageResponseTime());
        
        if (query.isIncludeMetrics()) {
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("errorCount", errorCount.get());
            metrics.put("uptime", java.time.Duration.between(startTime, LocalDateTime.now()).toMinutes());
            metrics.put("capabilities", capabilities);
            metrics.put("collaborators", collaborators.keySet());
            response.setMetrics(metrics);
        }
        
        sender.tell(response, self);
    }
    
    /**
     * 处理字符串命令
     */
    protected void handleStringCommand(String cmd) {
        switch (cmd.toLowerCase()) {
            case "status" -> {
                AgentStatusQuery query = new AgentStatusQuery(agentName, true);
                handleStatusQuery(query);
            }
            case "reset" -> resetAgent();
            case "capabilities" -> {
                log.info("Agent '{}' 能力: {}", agentName, capabilities);
                sender.tell("能力: " + String.join(", ", capabilities), self);
            }
            default -> {
                log.warn("Agent '{}' 收到未知命令: {}", agentName, cmd);
                sender.tell("未知命令: " + cmd, self);
            }
        }
    }
    
    // ==================== 抽象方法（子类必须实现） ====================
    
    /**
     * 处理思考请求的具体实现
     */
    protected abstract void processThinkingRequest(ThinkingRequest request);
    
    /**
     * 检查是否能处理协作请求
     */
    protected abstract boolean canHandleCollaboration(CollaborationRequest request);
    
    /**
     * 处理协作请求的具体实现
     */
    protected abstract void processCollaborationRequest(CollaborationRequest request);
    
    // ==================== 可选重写方法 ====================
    
    protected void handleThinkingResponse(ThinkingResponse response) {
        log.info("Agent '{}' 收到思考响应", agentName);
    }
    
    protected void handleKnowledgeQuery(KnowledgeQuery query) {
        log.info("Agent '{}' 收到知识查询: {}", agentName, query.getTopic());
    }
    
    protected void handleKnowledgeResponse(KnowledgeResponse response) {
        log.info("Agent '{}' 收到知识响应", agentName);
    }
    
    protected void handleKnowledgeUpdate(KnowledgeUpdate update) {
        log.info("Agent '{}' 收到知识更新", agentName);
    }
    
    protected void handleCollaborationResponse(CollaborationResponse response) {
        log.info("Agent '{}' 收到协作响应", agentName);
    }
    
    protected void handleLearningRequest(LearningRequest request) {
        log.info("Agent '{}' 收到学习请求: {}", agentName, request.getTopic());
    }
    
    protected void handleLearningProgress(LearningProgress progress) {
        log.info("Agent '{}' 收到学习进度: {}%", agentName, progress.getProgress() * 100);
    }
    
    protected void handleCustomMessage(Object message) {
        log.warn("Agent '{}' 收到未处理的消息类型: {}", agentName, message.getClass());
    }
    
    protected void handleError(Object message, Exception error) {
        log.error("Agent '{}' 处理消息失败: {} - {}", 
            agentName, message.getClass().getSimpleName(), error.getMessage());
        currentState = AgentState.ERROR;
        
        // 可以发送错误通知给监控系统
        scheduleIn("recover", 5000L); // 5秒后尝试恢复
    }
    
    // ==================== 工具方法 ====================
    
    /**
     * 重置Agent状态
     */
    protected void resetAgent() {
        currentState = AgentState.IDLE;
        activeTasks.clear();
        errorCount.set(0);
        log.info("Agent '{}' 已重置", agentName);
    }
    
    /**
     * 计算平均响应时间
     */
    protected double calculateAverageResponseTime() {
        long totalMessages = totalProcessedMessages.get();
        if (totalMessages == 0) return 0.0;
        return (double) totalResponseTime.get() / totalMessages;
    }
    
    /**
     * 添加协作者
     */
    protected void addCollaborator(String name, ActorRef ref) {
        collaborators.put(name, ref);
        log.info("Agent '{}' 添加协作者: {}", agentName, name);
    }
    
    /**
     * 移除协作者
     */
    protected void removeCollaborator(String name) {
        collaborators.remove(name);
        log.info("Agent '{}' 移除协作者: {}", agentName, name);
    }
    
    /**
     * 任务上下文类
     */
    protected static class TaskContext {
        @Getter private final String taskId;
        @Getter private final String taskType;
        @Getter private final String description;
        @Getter private final LocalDateTime startTime;
        @Getter private volatile String status = "RUNNING";
        
        public TaskContext(String taskId, String taskType, String description) {
            this.taskId = taskId;
            this.taskType = taskType;
            this.description = description;
            this.startTime = LocalDateTime.now();
        }
        
        public void setStatus(String status) {
            this.status = status;
        }
    }
}
