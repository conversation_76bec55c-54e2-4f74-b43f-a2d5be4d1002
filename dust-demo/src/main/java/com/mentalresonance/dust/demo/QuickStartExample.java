package com.mentalresonance.dust.demo;

import com.mentalresonance.dust.core.actors.ActorSystem;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.services.GsonPersistenceService;
import com.mentalresonance.dust.demo.actors.HelloActor;
import com.mentalresonance.dust.demo.actors.CounterActor;
import com.mentalresonance.dust.demo.actors.PersistentCounterActor;
import com.mentalresonance.dust.demo.actors.ParentActor;
import lombok.extern.slf4j.Slf4j;

/**
 * Dust-Core快速入门演示
 * 
 * 这个类演示了Dust-Core框架的基本用法，包括：
 * 1. 创建Actor系统
 * 2. 创建不同类型的Actor
 * 3. 发送消息和接收响应
 * 4. 持久化Actor的使用
 * 5. 父子Actor的管理
 * 6. 系统的优雅关闭
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class QuickStartExample {
    
    public static void main(String[] args) {
        log.info("=== Dust-Core 快速入门演示开始 ===");
        
        try {
            // 1. 创建Actor系统
            log.info("\n1. 创建Actor系统...");
            ActorSystem system = new ActorSystem("QuickStartSystem");
            
            // 2. 设置持久化服务（用于持久化Actor）
            log.info("\n2. 设置持久化服务...");
            system.setPersistenceService(GsonPersistenceService.create());

            // 3. 演示基本的HelloActor
            log.info("\n3. 演示HelloActor...");
            demonstrateHelloActor(system);
            
            Thread.sleep(2000); // 等待消息处理完成
            
            // 4. 演示CounterActor
            log.info("\n4. 演示CounterActor...");
            demonstrateCounterActor(system);
            
            Thread.sleep(2000);
            
            // 5. 演示PersistentCounterActor
            log.info("\n5. 演示PersistentCounterActor...");
            demonstratePersistentCounterActor(system);
            
            Thread.sleep(2000);
            
            // 6. 演示ParentActor和子Actor管理
            log.info("\n6. 演示ParentActor和子Actor管理...");
            demonstrateParentChildActors(system);
            
            Thread.sleep(3000);
            
            // 7. 优雅关闭系统
            log.info("\n7. 关闭Actor系统...");
            system.stop();
            
            log.info("\n=== Dust-Core 快速入门演示结束 ===");
            
        } catch (Exception e) {
            log.error("演示过程中发生错误", e);
        }
    }
    
    /**
     * 演示HelloActor的基本用法
     */
    private static void demonstrateHelloActor(ActorSystem system) throws Exception {
        log.info("创建HelloActor...");
        
        // 创建HelloActor
        ActorRef helloActor = system.getContext().actorOf(
            HelloActor.props("DemoHello"), "hello-actor");
        
        // 发送不同类型的消息
        log.info("发送消息给HelloActor...");
        helloActor.tell("hello", null);
        helloActor.tell("ping", null);
        helloActor.tell("time", null);
        helloActor.tell(42, null);
        helloActor.tell(100L, null);
        helloActor.tell(true, null);
        helloActor.tell("自定义消息", null);
    }
    
    /**
     * 演示CounterActor的状态管理
     */
    private static void demonstrateCounterActor(ActorSystem system) throws Exception {
        log.info("创建CounterActor...");
        
        // 创建CounterActor
        ActorRef counterActor = system.getContext().actorOf(
            CounterActor.props("DemoCounter"), "counter-actor");
        
        // 发送各种计数器命令
        log.info("发送命令给CounterActor...");
        counterActor.tell("get", null);
        counterActor.tell("increment", null);
        counterActor.tell("inc", null);
        counterActor.tell("++", null);
        counterActor.tell("get", null);
        counterActor.tell("set:100", null);
        counterActor.tell("double", null);
        counterActor.tell("half", null);
        counterActor.tell(new CounterActor.IncrementMsg(5), null);
        counterActor.tell(CounterActor.QueryMsg.INSTANCE, null);
        counterActor.tell("reset", null);
    }
    
    /**
     * 演示PersistentCounterActor的持久化功能
     */
    private static void demonstratePersistentCounterActor(ActorSystem system) throws Exception {
        log.info("创建PersistentCounterActor...");
        
        // 创建PersistentCounterActor
        ActorRef persistentCounter = system.getContext().actorOf(
            PersistentCounterActor.props("DemoPersistentCounter"), "persistent-counter");
        
        // 发送命令测试持久化
        log.info("发送命令给PersistentCounterActor...");
        persistentCounter.tell("get", null);
        persistentCounter.tell("increment", null);
        persistentCounter.tell("increment", null);
        persistentCounter.tell("set:50", null);
        persistentCounter.tell("status", null);
        persistentCounter.tell("save", null); // 手动保存快照
        
        // 模拟重启（停止后重新创建）
        Thread.sleep(1000);
        log.info("模拟Actor重启...");
        system.getContext().stop(persistentCounter);
        
        Thread.sleep(1000);
        
        // 重新创建，应该恢复之前的状态
        ActorRef restoredCounter = system.getContext().actorOf(
            PersistentCounterActor.props("DemoPersistentCounter"), "persistent-counter-restored");
        
        restoredCounter.tell("get", null);
        restoredCounter.tell("status", null);
    }
    
    /**
     * 演示ParentActor和子Actor的管理
     */
    private static void demonstrateParentChildActors(ActorSystem system) throws Exception {
        log.info("创建ParentActor...");
        
        // 创建ParentActor
        ActorRef parentActor = system.getContext().actorOf(
            ParentActor.props("DemoParent"), "parent-actor");
        
        // 等待子Actor创建完成
        Thread.sleep(1000);
        
        // 查看子Actor列表
        log.info("查看子Actor列表...");
        parentActor.tell("list", null);
        
        Thread.sleep(500);
        
        // 创建新的子Actor
        log.info("创建新的子Actor...");
        parentActor.tell(new ParentActor.CreateChildMsg("hello", "new-hello"), null);
        parentActor.tell(new ParentActor.CreateChildMsg("persistent-counter", "new-counter"), null);
        
        Thread.sleep(1000);
        
        // 再次查看子Actor列表
        parentActor.tell("list", null);
        
        Thread.sleep(500);
        
        // 转发消息给子Actor
        log.info("转发消息给子Actor...");
        parentActor.tell(new ParentActor.ForwardMsg("hello", "Hello from parent!"), null);
        parentActor.tell(new ParentActor.ForwardMsg("counter", "increment"), null);
        parentActor.tell(new ParentActor.ForwardMsg("new-hello", "ping"), null);
        parentActor.tell(new ParentActor.ForwardMsg("new-counter", "set:999"), null);
        
        Thread.sleep(1000);
        
        // 广播消息给所有子Actor
        log.info("广播消息给所有子Actor...");
        parentActor.tell(new ParentActor.BroadcastMsg("这是广播消息"), null);
        
        Thread.sleep(1000);
        
        // 停止一个子Actor
        log.info("停止一个子Actor...");
        parentActor.tell(new ParentActor.StopChildMsg("new-hello"), null);
        
        Thread.sleep(1000);
        
        // 查看剩余的子Actor
        parentActor.tell("list", null);
        
        Thread.sleep(500);
        
        // 使用字符串命令转发消息
        log.info("使用字符串命令转发消息...");
        parentActor.tell("forward:counter:get", null);
        parentActor.tell("forward:new-counter:status", null);
    }
}
