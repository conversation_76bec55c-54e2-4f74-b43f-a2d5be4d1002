package com.mentalresonance.dust.demo.examples;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.ActorSystem;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.actors.SupervisionStrategy;
import com.mentalresonance.dust.core.msgs.Terminated;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 错误处理和监督策略演示
 * 
 * 这个例子演示了：
 * 1. Actor中的异常处理
 * 2. 监督策略的使用
 * 3. Actor的重启和恢复机制
 * 4. 错误传播和处理
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class ErrorHandlingExample {
    
    public static void main(String[] args) {
        log.info("=== 错误处理演示开始 ===");
        
        try {
            ActorSystem system = new ActorSystem("ErrorHandlingSystem");
            
            // 创建监督Actor
            ActorRef supervisor = system.getContext().actorOf(
                SupervisorActor.props(), "supervisor");
            
            // 发送各种消息来触发不同的错误情况
            supervisor.tell("create-workers", null);
            
            Thread.sleep(1000);
            
            // 触发不同类型的异常
            supervisor.tell("test-runtime-exception", null);
            supervisor.tell("test-illegal-argument", null);
            supervisor.tell("test-null-pointer", null);
            supervisor.tell("test-arithmetic-exception", null);
            
            Thread.sleep(3000);
            
            // 查看Worker状态
            supervisor.tell("check-workers", null);
            
            Thread.sleep(2000);
            
            system.stop();
            log.info("=== 错误处理演示结束 ===");
            
        } catch (Exception e) {
            log.error("演示过程中发生错误", e);
        }
    }
    
    /**
     * 监督Actor：管理Worker Actor并处理它们的异常
     */
    @Slf4j
    public static class SupervisorActor extends Actor {
        
        private ActorRef worker1;
        private ActorRef worker2;
        private ActorRef worker3;
        
        public static Props props() {
            return Props.create(SupervisorActor.class);
        }
        
        @Override
        protected void preStart() {
            // 设置监督策略
            supervisor = new CustomSupervisionStrategy();
            log.info("监督Actor已启动，设置了自定义监督策略");
        }
        
        @Override
        protected ActorBehavior createBehavior() {
            return message -> {
                switch (message) {
                    case String cmd -> {
                        switch (cmd) {
                            case "create-workers" -> createWorkers();
                            case "test-runtime-exception" -> testRuntimeException();
                            case "test-illegal-argument" -> testIllegalArgument();
                            case "test-null-pointer" -> testNullPointer();
                            case "test-arithmetic-exception" -> testArithmeticException();
                            case "check-workers" -> checkWorkers();
                            default -> log.warn("监督Actor收到未知命令: {}", cmd);
                        }
                    }
                    
                    case Terminated terminated -> {
                        log.warn("监督Actor收到Worker终止通知: {}", terminated.getName());
                        // 可以在这里重新创建Worker
                    }
                    
                    default -> {
                        log.warn("监督Actor收到未知消息: {}", message);
                    }
                }
            };
        }
        
        private void createWorkers() {
            try {
                worker1 = actorOf(WorkerActor.props("Worker1"), "worker1");
                worker2 = actorOf(WorkerActor.props("Worker2"), "worker2");
                worker3 = actorOf(WorkerActor.props("Worker3"), "worker3");
                
                // 监视Worker
                watch(worker1);
                watch(worker2);
                watch(worker3);
                
                log.info("监督Actor创建了3个Worker");
            } catch (Exception e) {
                log.error("创建Worker失败", e);
            }
        }
        
        private void testRuntimeException() {
            log.info("测试RuntimeException...");
            if (worker1 != null) {
                worker1.tell(new ErrorCommand("runtime"), self);
            }
        }
        
        private void testIllegalArgument() {
            log.info("测试IllegalArgumentException...");
            if (worker2 != null) {
                worker2.tell(new ErrorCommand("illegal-argument"), self);
            }
        }
        
        private void testNullPointer() {
            log.info("测试NullPointerException...");
            if (worker3 != null) {
                worker3.tell(new ErrorCommand("null-pointer"), self);
            }
        }
        
        private void testArithmeticException() {
            log.info("测试ArithmeticException...");
            if (worker1 != null) {
                worker1.tell(new ErrorCommand("arithmetic"), self);
            }
        }
        
        private void checkWorkers() {
            log.info("检查Worker状态...");
            if (worker1 != null) worker1.tell("status", self);
            if (worker2 != null) worker2.tell("status", self);
            if (worker3 != null) worker3.tell("status", self);
        }
    }
    
    /**
     * Worker Actor：执行任务并可能抛出异常
     */
    @Slf4j
    public static class WorkerActor extends Actor {
        
        private final String workerName;
        private int taskCount = 0;
        private int errorCount = 0;
        
        public static Props props(String workerName) {
            return Props.create(WorkerActor.class, workerName);
        }
        
        public WorkerActor(String workerName) {
            this.workerName = workerName;
        }
        
        @Override
        protected void preStart() {
            log.info("Worker '{}' 已启动", workerName);
        }
        
        @Override
        protected ActorBehavior createBehavior() {
            return message -> {
                switch (message) {
                    case ErrorCommand cmd -> {
                        taskCount++;
                        log.info("Worker '{}' 收到错误命令: {}", workerName, cmd.errorType);
                        
                        // 根据命令类型抛出不同的异常
                        switch (cmd.errorType) {
                            case "runtime" -> {
                                errorCount++;
                                throw new RuntimeException("Worker " + workerName + " 运行时异常");
                            }
                            case "illegal-argument" -> {
                                errorCount++;
                                throw new IllegalArgumentException("Worker " + workerName + " 非法参数异常");
                            }
                            case "null-pointer" -> {
                                errorCount++;
                                throw new NullPointerException("Worker " + workerName + " 空指针异常");
                            }
                            case "arithmetic" -> {
                                errorCount++;
                                int result = 10 / 0; // 除零异常
                            }
                            default -> {
                                log.warn("Worker '{}' 未知错误类型: {}", workerName, cmd.errorType);
                            }
                        }
                    }
                    
                    case String cmd when "status".equals(cmd) -> {
                        String status = String.format("Worker '%s' - 任务数: %d, 错误数: %d", 
                            workerName, taskCount, errorCount);
                        log.info(status);
                        sender.tell(status, self);
                    }
                    
                    case String cmd when "reset".equals(cmd) -> {
                        taskCount = 0;
                        errorCount = 0;
                        log.info("Worker '{}' 状态已重置", workerName);
                    }
                    
                    default -> {
                        taskCount++;
                        log.info("Worker '{}' 处理正常消息: {}", workerName, message);
                    }
                }
            };
        }
        
        @Override
        protected void preRestart(Throwable reason) {
            log.warn("Worker '{}' 即将重启，原因: {}", workerName, reason.getMessage());
            errorCount++; // 记录错误次数
        }
        
        @Override
        protected void onResume() {
            log.info("Worker '{}' 已恢复", workerName);
        }
        
        @Override
        protected void postStop() {
            log.info("Worker '{}' 已停止，最终状态 - 任务数: {}, 错误数: {}", 
                workerName, taskCount, errorCount);
        }
    }
    
    /**
     * 自定义监督策略
     */
    public static class CustomSupervisionStrategy extends SupervisionStrategy {
        
        public CustomSupervisionStrategy() {
            super(SS_RESTART, MODE_ONE_FOR_ONE);
        }
        
        @Override
        protected SupervisionStrategy strategy(ActorRef ref, Throwable thrown) {
            log.info("监督策略处理异常: {} - {}", ref.path, thrown.getClass().getSimpleName());
            
            // 根据异常类型决定不同的处理策略
            if (thrown instanceof IllegalArgumentException) {
                log.info("IllegalArgumentException -> 恢复Actor");
                return new SupervisionStrategy(SS_RESUME, MODE_ONE_FOR_ONE);
            } else if (thrown instanceof NullPointerException) {
                log.info("NullPointerException -> 重启Actor");
                return new SupervisionStrategy(SS_RESTART, MODE_ONE_FOR_ONE);
            } else if (thrown instanceof ArithmeticException) {
                log.info("ArithmeticException -> 停止Actor");
                return new SupervisionStrategy(SS_STOP, MODE_ONE_FOR_ONE);
            } else {
                log.info("其他异常 -> 重启Actor");
                return new SupervisionStrategy(SS_RESTART, MODE_ONE_FOR_ONE);
            }
        }
    }
    
    /**
     * 错误命令消息
     */
    public static class ErrorCommand implements Serializable {
        public final String errorType;
        
        public ErrorCommand(String errorType) {
            this.errorType = errorType;
        }
        
        @Override
        public String toString() {
            return "ErrorCommand{errorType='" + errorType + "'}";
        }
    }
}
