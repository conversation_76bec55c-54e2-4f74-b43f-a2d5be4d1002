package com.mentalresonance.dust.demo.actors;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.Props;
import lombok.extern.slf4j.Slf4j;

/**
 * Hello Actor演示：展示基本的Actor消息处理
 * 
 * 这是一个简单的Actor实现，演示了：
 * 1. 如何创建Actor
 * 2. 如何处理不同类型的消息
 * 3. 如何回复消息给发送者
 * 4. 基本的日志记录
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class HelloActor extends Actor {
    
    /**
     * Actor的名称
     */
    private final String name;
    
    /**
     * 创建Props的静态方法
     * 
     * Props是Actor的配置对象，包含了创建Actor所需的所有信息。
     * 使用静态方法创建Props是推荐的做法。
     * 
     * @param name Actor的名称
     * @return Props配置对象
     */
    public static Props props(String name) {
        return Props.create(HelloActor.class, name);
    }
    
    /**
     * 构造函数
     * 
     * @param name Actor的名称
     */
    public HelloActor(String name) {
        this.name = name;
        log.info("HelloActor '{}' 已创建", name);
    }
    
    /**
     * Actor启动前的初始化
     * 
     * 这个方法在Actor开始处理消息之前被调用，
     * 可以用来进行一些初始化工作。
     */
    @Override
    protected void preStart() {
        log.info("HelloActor '{}' 正在启动...", name);
    }
    
    /**
     * 定义Actor的消息处理行为
     * 
     * 这是Actor的核心方法，定义了如何处理接收到的消息。
     * 使用Java 21的模式匹配特性来处理不同类型的消息。
     * 
     * @return ActorBehavior消息处理行为
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                // 处理字符串消息
                case String msg -> {
                    log.info("{} 收到字符串消息: {}", name, msg);
                    
                    // 根据消息内容进行不同的处理
                    if ("hello".equalsIgnoreCase(msg)) {
                        // 回复问候消息
                        sender.tell("你好！我是 " + name, self);
                    } else if ("ping".equalsIgnoreCase(msg)) {
                        // 回复ping消息
                        sender.tell("pong", self);
                    } else if ("time".equalsIgnoreCase(msg)) {
                        // 回复当前时间
                        sender.tell("当前时间: " + java.time.LocalDateTime.now(), self);
                    } else {
                        // 回复收到的消息
                        sender.tell("收到消息: " + msg, self);
                    }
                }
                
                // 处理整数消息
                case Integer number -> {
                    log.info("{} 收到数字: {}", name, number);
                    
                    // 计算并回复结果
                    int result = number * 2;
                    sender.tell(result, self);
                    log.info("{} 计算结果: {} * 2 = {}", name, number, result);
                }
                
                // 处理长整数消息
                case Long longNumber -> {
                    log.info("{} 收到长整数: {}", name, longNumber);
                    
                    // 计算平方并回复
                    long result = longNumber * longNumber;
                    sender.tell(result, self);
                    log.info("{} 计算结果: {} ^ 2 = {}", name, longNumber, result);
                }
                
                // 处理布尔值消息
                case Boolean flag -> {
                    log.info("{} 收到布尔值: {}", name, flag);
                    
                    String response = flag ? "是的，这是真的！" : "不，这是假的！";
                    sender.tell(response, self);
                }
                
                // 处理未知消息类型
                default -> {
                    log.warn("{} 收到未知消息类型: {} - {}", 
                        name, message.getClass().getSimpleName(), message);
                    
                    // 回复错误信息
                    sender.tell("抱歉，我不知道如何处理这种类型的消息: " + 
                        message.getClass().getSimpleName(), self);
                }
            }
        };
    }
    
    /**
     * Actor停止后的清理工作
     * 
     * 这个方法在Actor停止后被调用，
     * 可以用来进行一些清理工作。
     */
    @Override
    protected void postStop() {
        log.info("HelloActor '{}' 已停止", name);
    }
    
    /**
     * Actor重启前的处理
     * 
     * 当Actor因为异常而重启时，这个方法会被调用。
     * 
     * @param reason 导致重启的异常
     */
    @Override
    protected void preRestart(Throwable reason) {
        log.warn("HelloActor '{}' 即将重启，原因: {}", name, reason.getMessage());
    }
    
    /**
     * Actor恢复时的处理
     * 
     * 当Actor从异常中恢复时，这个方法会被调用。
     */
    @Override
    protected void onResume() {
        log.info("HelloActor '{}' 已恢复", name);
    }
}
