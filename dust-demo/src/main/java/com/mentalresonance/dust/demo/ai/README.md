# Dust-Core AI Agent 系统

这是一个基于Dust-Core框架构建的高级AI Agent系统，展示了如何使用Actor模型实现复杂的多Agent人工智能系统。

## 🎯 系统概述

本系统实现了一个完整的多Agent AI架构，包含以下核心组件：

### 🧠 核心Agent

1. **DeepThinkingAgent (深度思考Agent)**
   - 专注于复杂推理和深度分析
   - 支持多种思考模式：分析、创造、批判、深度推理
   - 实现多层次推理链构建
   - 具备不确定性量化能力

2. **KnowledgeManagerAgent (知识管理Agent)**
   - 智能知识存储和检索系统
   - 支持语义相似度计算
   - 动态知识图谱构建
   - 知识质量评估和版本管理

3. **CoordinatorAgent (协调Agent)**
   - 多Agent系统的大脑
   - 智能任务分解和分发
   - Agent能力匹配和负载均衡
   - 结果整合和综合

4. **LearningAgent (学习Agent)**
   - 自主学习和适应能力
   - 支持多种学习模式：监督、无监督、强化、迁移、增量
   - 经验积累和模式识别
   - 性能自我评估和优化

### 🔄 系统架构

```
用户输入
    ↓
CoordinatorAgent (任务分析和分发)
    ↓
┌─────────────────┬─────────────────┬─────────────────┐
│ DeepThinkingAgent │ KnowledgeManagerAgent │ LearningAgent │
│   (复杂推理)      │    (知识检索)         │   (学习优化)   │
└─────────────────┴─────────────────┴─────────────────┘
    ↓
结果整合和响应
    ↓
用户输出
```

## 🚀 核心特性

### 1. 智能任务分发
- **复杂度分析**: 自动分析任务复杂度，选择最佳处理策略
- **能力匹配**: 根据Agent能力和任务需求进行智能匹配
- **负载均衡**: 避免单个Agent过载，优化系统性能

### 2. 多模式思考
- **分析模式**: 逻辑分析和演绎推理
- **创造模式**: 发散思维和创新思考
- **批判模式**: 批判性思维和质疑分析
- **深度推理**: 多层次递归推理
- **协作模式**: 多Agent协同思考

### 3. 知识驱动推理
- **动态知识库**: 实时更新的知识存储系统
- **语义检索**: 基于语义相似度的智能检索
- **知识图谱**: 构建知识间的关联关系
- **质量评估**: 自动评估知识的可信度和相关性

### 4. 自主学习能力
- **经验学习**: 从交互中学习成功模式
- **强化学习**: 基于反馈优化决策策略
- **迁移学习**: 从相似领域迁移知识
- **增量学习**: 持续更新和改进知识

### 5. 容错和恢复
- **状态持久化**: 自动保存Agent状态
- **异常处理**: 完善的错误处理机制
- **自动恢复**: 系统故障后的自动恢复
- **监督策略**: 多层次的监督和重启机制

## 📋 使用示例

### 基本使用

```java
// 创建AI Agent系统
ActorSystem system = ActorSystem.create("AIAgentSystem");

// 创建协调Agent
ActorRef coordinator = system.getContext().actorOf(
    CoordinatorAgent.props("CoordinatorAgent"), "CoordinatorAgent");

// 发送思考请求
ThinkingRequest request = new ThinkingRequest(
    "分析人工智能对未来社会的影响", 
    "学术研究背景", 
    ThinkingMode.ANALYTICAL);

coordinator.tell(request, null);
```

### 交互式使用

```bash
# 运行交互式演示
mvn exec:java -Dexec.mainClass="com.mentalresonance.dust.demo.ai.AIAgentSystemDemo"

# 系统启动后，您可以：
🤖 请输入您的问题: 什么是机器学习？

# 使用系统命令
🤖 请输入您的问题: /status    # 查看系统状态
🤖 请输入您的问题: /agents    # 查看已注册Agent
🤖 请输入您的问题: /demo      # 运行演示场景
```

## 🔧 配置和扩展

### 添加新的Agent类型

1. 继承 `BaseAIAgent` 类
2. 实现必要的抽象方法
3. 定义Agent特有的能力
4. 在协调Agent中注册新Agent

```java
public class CustomAgent extends BaseAIAgent {
    public CustomAgent(String agentName) {
        super(agentName, "CustomAgent");
    }
    
    @Override
    protected void initializeCapabilities() {
        capabilities.add("自定义能力");
    }
    
    // 实现其他必要方法...
}
```

### 自定义思考模式

```java
// 在DeepThinkingAgent中添加新的推理策略
private class CustomStrategy implements ReasoningStrategy {
    @Override
    public List<ReasoningStep> reason(String query, String context, int maxDepth) {
        // 实现自定义推理逻辑
        return customReasoningSteps;
    }
}
```

### 扩展知识管理

```java
// 添加新的知识源
KnowledgeItem newKnowledge = new KnowledgeItem();
newKnowledge.setTitle("新知识");
newKnowledge.setContent("知识内容");
newKnowledge.setDomain("专业领域");

KnowledgeUpdate update = new KnowledgeUpdate(
    newKnowledge, UpdateOperation.CREATE, "添加新知识");

knowledgeManager.tell(update, null);
```

## 🎮 演示场景

系统包含多个预设的演示场景：

1. **技术分析场景**: "分析机器学习和深度学习的区别"
2. **创新设计场景**: "设计一个智能家居系统"
3. **批判思维场景**: "评估AI技术的局限性"
4. **复杂推理场景**: "分析量子计算对AI的影响"
5. **知识查询场景**: "什么是自然语言处理？"

## 📊 性能监控

系统提供完整的性能监控功能：

- **响应时间**: 监控各Agent的响应时间
- **准确率**: 跟踪推理结果的准确性
- **用户满意度**: 基于反馈的满意度评估
- **学习进度**: 监控学习Agent的学习效果
- **系统负载**: 监控系统资源使用情况

## 🔍 技术亮点

### 1. Actor模型的优势
- **并发处理**: 天然支持高并发消息处理
- **容错性**: 完善的监督策略和错误恢复
- **可扩展性**: 易于添加新的Agent类型
- **分布式**: 支持跨节点的Agent通信

### 2. 智能决策机制
- **动态策略选择**: 根据任务特点选择最佳策略
- **多Agent协作**: 复杂任务的智能分解和协作
- **学习优化**: 基于历史经验的持续优化
- **上下文感知**: 考虑上下文信息的智能决策

### 3. 知识管理创新
- **语义理解**: 基于语义的知识检索和匹配
- **动态更新**: 实时的知识更新和版本管理
- **质量控制**: 自动的知识质量评估机制
- **关联发现**: 智能的知识关联关系发现

## 🛠️ 开发指南

### 环境要求
- Java 21+
- Maven 3.6+
- 8GB+ RAM (推荐)

### 编译和运行
```bash
# 编译项目
mvn clean compile

# 运行AI Agent系统
mvn exec:java -Dexec.mainClass="com.mentalresonance.dust.demo.ai.AIAgentSystemDemo"

# 打包
mvn clean package
```

### 调试和测试
```bash
# 启用调试日志
export JAVA_OPTS="-Dlogback.configurationFile=logback-debug.xml"

# 运行特定测试
mvn test -Dtest=AIAgentSystemTest
```

## 🔮 未来扩展

1. **多模态支持**: 支持文本、图像、语音等多种输入
2. **分布式部署**: 支持跨多个节点的Agent部署
3. **实时学习**: 实现真正的在线学习能力
4. **情感计算**: 添加情感理解和表达能力
5. **领域专家**: 开发特定领域的专家Agent

## 📝 许可证

本项目遵循与Dust-Core框架相同的许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个AI Agent系统！

---

这个AI Agent系统展示了Dust-Core框架在构建复杂AI系统方面的强大能力，为开发者提供了一个完整的参考实现。
