package com.mentalresonance.dust.demo.ai.messages;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * AI Agent系统消息定义
 * 
 * 定义了AI Agent系统中使用的各种消息类型，包括：
 * - 思考请求和响应
 * - 知识查询和更新
 * - Agent间协作消息
 * - 推理链消息
 * 
 * <AUTHOR> Team
 */
public class AIMessages {
    
    // ==================== 基础消息类 ====================
    
    /**
     * 基础AI消息：所有AI消息的基类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static abstract class BaseAIMessage implements Serializable {
        protected String messageId = UUID.randomUUID().toString();
        protected LocalDateTime timestamp = LocalDateTime.now();
        protected String sessionId;
        protected int priority = 5; // 1-10, 10最高
    }
    
    // ==================== 思考相关消息 ====================
    
    /**
     * 思考请求：请求AI Agent进行深度思考
     */
    @Data
    @NoArgsConstructor
    public static class ThinkingRequest extends BaseAIMessage {
        private String query;                    // 用户查询
        private String context;                  // 上下文信息
        private ThinkingMode mode;              // 思考模式
        private int maxDepth = 5;               // 最大思考深度
        private long timeoutMs = 30000;         // 超时时间
        private Map<String, Object> parameters; // 额外参数
        
        public ThinkingRequest(String query, String context, ThinkingMode mode) {
            super();
            this.query = query;
            this.context = context;
            this.mode = mode;
        }
    }
    
    /**
     * 思考响应：AI Agent的思考结果
     */
    @Data
    @NoArgsConstructor
    public static class ThinkingResponse extends BaseAIMessage {
        private String originalQuery;           // 原始查询
        private String finalAnswer;             // 最终答案
        private List<ReasoningStep> reasoningChain; // 推理链
        private double confidence;              // 置信度 0-1
        private long processingTimeMs;          // 处理时间
        private Map<String, Object> metadata;  // 元数据
        
        public ThinkingResponse(String originalQuery, String finalAnswer, 
                              List<ReasoningStep> reasoningChain, double confidence) {
            super();
            this.originalQuery = originalQuery;
            this.finalAnswer = finalAnswer;
            this.reasoningChain = reasoningChain;
            this.confidence = confidence;
        }
    }
    
    /**
     * 推理步骤：思考过程中的单个推理步骤
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReasoningStep implements Serializable {
        private int stepNumber;                 // 步骤编号
        private String stepType;                // 步骤类型
        private String description;             // 步骤描述
        private String input;                   // 输入
        private String output;                  // 输出
        private double confidence;              // 该步骤的置信度
        private long durationMs;                // 执行时间
        private Map<String, Object> details;   // 详细信息
    }
    
    /**
     * 思考模式枚举
     */
    public enum ThinkingMode {
        ANALYTICAL,      // 分析模式：逻辑分析
        CREATIVE,        // 创造模式：创意思考
        CRITICAL,        // 批判模式：批判性思维
        COLLABORATIVE,   // 协作模式：多Agent协作
        DEEP_REASONING,  // 深度推理：复杂推理链
        FAST_RESPONSE    // 快速响应：简单直接
    }
    
    // ==================== 知识管理消息 ====================
    
    /**
     * 知识查询请求
     */
    @Data
    @NoArgsConstructor
    public static class KnowledgeQuery extends BaseAIMessage {
        private String topic;                   // 查询主题
        private List<String> keywords;          // 关键词
        private String domain;                  // 领域
        private int maxResults = 10;            // 最大结果数
        private double minRelevance = 0.7;      // 最小相关度
        
        public KnowledgeQuery(String topic, List<String> keywords, String domain) {
            super();
            this.topic = topic;
            this.keywords = keywords;
            this.domain = domain;
        }
    }
    
    /**
     * 知识查询响应
     */
    @Data
    @NoArgsConstructor
    public static class KnowledgeResponse extends BaseAIMessage {
        private String query;                   // 原始查询
        private List<KnowledgeItem> results;    // 查询结果
        private int totalFound;                 // 总找到数量
        private double avgRelevance;            // 平均相关度
        
        public KnowledgeResponse(String query, List<KnowledgeItem> results) {
            super();
            this.query = query;
            this.results = results;
            this.totalFound = results.size();
        }
    }
    
    /**
     * 知识项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KnowledgeItem implements Serializable {
        private String id;                      // 知识ID
        private String title;                   // 标题
        private String content;                 // 内容
        private String domain;                  // 领域
        private List<String> tags;              // 标签
        private double relevance;               // 相关度
        private LocalDateTime lastUpdated;      // 最后更新时间
        private String source;                  // 来源
    }
    
    /**
     * 知识更新请求
     */
    @Data
    @NoArgsConstructor
    public static class KnowledgeUpdate extends BaseAIMessage {
        private KnowledgeItem item;             // 要更新的知识项
        private UpdateOperation operation;      // 操作类型
        private String reason;                  // 更新原因
        
        public KnowledgeUpdate(KnowledgeItem item, UpdateOperation operation, String reason) {
            super();
            this.item = item;
            this.operation = operation;
            this.reason = reason;
        }
    }
    
    /**
     * 更新操作类型
     */
    public enum UpdateOperation {
        CREATE,     // 创建
        UPDATE,     // 更新
        DELETE,     // 删除
        MERGE       // 合并
    }
    
    // ==================== Agent协作消息 ====================
    
    /**
     * Agent协作请求
     */
    @Data
    @NoArgsConstructor
    public static class CollaborationRequest extends BaseAIMessage {
        private String requestingAgent;         // 请求Agent
        private String targetAgent;             // 目标Agent
        private String task;                    // 任务描述
        private CollaborationType type;         // 协作类型
        private Map<String, Object> sharedData; // 共享数据
        private long deadline;                  // 截止时间
        
        public CollaborationRequest(String requestingAgent, String targetAgent, 
                                  String task, CollaborationType type) {
            super();
            this.requestingAgent = requestingAgent;
            this.targetAgent = targetAgent;
            this.task = task;
            this.type = type;
        }
    }
    
    /**
     * Agent协作响应
     */
    @Data
    @NoArgsConstructor
    public static class CollaborationResponse extends BaseAIMessage {
        private String originalRequestId;       // 原始请求ID
        private String respondingAgent;         // 响应Agent
        private boolean accepted;               // 是否接受
        private String result;                  // 结果
        private Map<String, Object> resultData; // 结果数据
        private String feedback;                // 反馈
        
        public CollaborationResponse(String originalRequestId, String respondingAgent, 
                                   boolean accepted, String result) {
            super();
            this.originalRequestId = originalRequestId;
            this.respondingAgent = respondingAgent;
            this.accepted = accepted;
            this.result = result;
        }
    }
    
    /**
     * 协作类型
     */
    public enum CollaborationType {
        CONSULTATION,   // 咨询
        DELEGATION,     // 委托
        COLLABORATION,  // 协作
        REVIEW,         // 审查
        BRAINSTORM      // 头脑风暴
    }
    
    // ==================== 系统控制消息 ====================
    
    /**
     * Agent状态查询
     */
    @Data
    @NoArgsConstructor
    public static class AgentStatusQuery extends BaseAIMessage {
        private String targetAgent;            // 目标Agent
        private boolean includeMetrics;        // 是否包含指标
        
        public AgentStatusQuery(String targetAgent, boolean includeMetrics) {
            super();
            this.targetAgent = targetAgent;
            this.includeMetrics = includeMetrics;
        }
    }
    
    /**
     * Agent状态响应
     */
    @Data
    @NoArgsConstructor
    public static class AgentStatusResponse extends BaseAIMessage {
        private String agentName;               // Agent名称
        private AgentState state;               // 当前状态
        private int activeTaskCount;            // 活跃任务数
        private long totalProcessedMessages;    // 总处理消息数
        private double avgResponseTime;         // 平均响应时间
        private Map<String, Object> metrics;   // 详细指标
        
        public AgentStatusResponse(String agentName, AgentState state) {
            super();
            this.agentName = agentName;
            this.state = state;
        }
    }
    
    /**
     * Agent状态枚举
     */
    public enum AgentState {
        IDLE,           // 空闲
        THINKING,       // 思考中
        COLLABORATING,  // 协作中
        LEARNING,       // 学习中
        BUSY,           // 忙碌
        ERROR           // 错误状态
    }
    
    // ==================== 学习相关消息 ====================
    
    /**
     * 学习请求：请求Agent学习新知识或技能
     */
    @Data
    @NoArgsConstructor
    public static class LearningRequest extends BaseAIMessage {
        private String topic;                   // 学习主题
        private List<String> materials;         // 学习材料
        private LearningMode mode;              // 学习模式
        private Map<String, Object> parameters; // 学习参数
        
        public LearningRequest(String topic, List<String> materials, LearningMode mode) {
            super();
            this.topic = topic;
            this.materials = materials;
            this.mode = mode;
        }
    }
    
    /**
     * 学习模式
     */
    public enum LearningMode {
        SUPERVISED,     // 监督学习
        UNSUPERVISED,   // 无监督学习
        REINFORCEMENT,  // 强化学习
        TRANSFER,       // 迁移学习
        INCREMENTAL     // 增量学习
    }
    
    /**
     * 学习进度报告
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LearningProgress extends BaseAIMessage {
        private String learningTaskId;          // 学习任务ID
        private double progress;                // 进度 0-1
        private String currentPhase;            // 当前阶段
        private List<String> learnedConcepts;   // 已学概念
        private Map<String, Double> metrics;   // 学习指标
    }
}
