package com.mentalresonance.dust.demo.ai.agents;

import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.demo.ai.messages.AIMessages.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 知识管理Agent：负责知识的存储、检索、更新和组织
 * 
 * 核心功能：
 * 1. 知识存储和索引
 * 2. 智能检索和匹配
 * 3. 知识图谱构建
 * 4. 知识质量评估
 * 5. 知识更新和版本管理
 * 6. 语义相似度计算
 * 
 * 知识组织结构：
 * - 领域分类：按学科领域组织知识
 * - 概念层次：构建概念的层次关系
 * - 关联网络：建立知识间的关联关系
 * - 时间维度：跟踪知识的时效性
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class KnowledgeManagerAgent extends BaseAIAgent {
    
    /**
     * 知识库：存储所有知识项
     */
    private final Map<String, KnowledgeItem> knowledgeBase = new ConcurrentHashMap<>();
    
    /**
     * 领域索引：按领域组织知识
     */
    private final Map<String, Set<String>> domainIndex = new ConcurrentHashMap<>();
    
    /**
     * 关键词索引：按关键词索引知识
     */
    private final Map<String, Set<String>> keywordIndex = new ConcurrentHashMap<>();
    
    /**
     * 知识关联图：知识间的关联关系
     */
    private final Map<String, Set<String>> knowledgeGraph = new ConcurrentHashMap<>();
    
    /**
     * 知识质量评分
     */
    private final Map<String, Double> qualityScores = new ConcurrentHashMap<>();
    
    /**
     * 访问统计
     */
    private final Map<String, Integer> accessCounts = new ConcurrentHashMap<>();
    
    /**
     * 创建Props
     */
    public static Props props(String agentName) {
        return Props.create(KnowledgeManagerAgent.class, agentName);
    }
    
    /**
     * 构造函数
     */
    public KnowledgeManagerAgent(String agentName) {
        super(agentName, "KnowledgeManagerAgent");
        initializeKnowledgeBase();
    }
    
    @Override
    protected void initializeCapabilities() {
        capabilities.add("知识存储");
        capabilities.add("智能检索");
        capabilities.add("语义匹配");
        capabilities.add("知识图谱");
        capabilities.add("质量评估");
        capabilities.add("版本管理");
        capabilities.add("关联分析");
        capabilities.add("知识推荐");
    }
    
    /**
     * 初始化知识库
     */
    private void initializeKnowledgeBase() {
        // 添加一些示例知识
        addKnowledge(createKnowledgeItem("ai-001", "人工智能基础", 
            "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
            "计算机科学", Arrays.asList("人工智能", "机器学习", "智能系统"), "系统初始化"));
            
        addKnowledge(createKnowledgeItem("ml-001", "机器学习概述",
            "机器学习是人工智能的一个子领域，专注于开发能够从数据中学习和改进的算法。",
            "计算机科学", Arrays.asList("机器学习", "算法", "数据科学"), "系统初始化"));
            
        addKnowledge(createKnowledgeItem("dl-001", "深度学习原理",
            "深度学习是机器学习的一个分支，使用多层神经网络来模拟人脑的学习过程。",
            "计算机科学", Arrays.asList("深度学习", "神经网络", "机器学习"), "系统初始化"));
            
        addKnowledge(createKnowledgeItem("nlp-001", "自然语言处理",
            "自然语言处理是人工智能的一个分支，专注于计算机与人类语言之间的交互。",
            "计算机科学", Arrays.asList("自然语言处理", "语言模型", "文本分析"), "系统初始化"));
            
        // 建立知识关联
        establishKnowledgeRelations();
        
        log.info("知识库初始化完成，包含 {} 个知识项", knowledgeBase.size());
    }
    
    /**
     * 建立知识关联关系
     */
    private void establishKnowledgeRelations() {
        // AI -> ML, DL, NLP
        addKnowledgeRelation("ai-001", "ml-001");
        addKnowledgeRelation("ai-001", "dl-001");
        addKnowledgeRelation("ai-001", "nlp-001");
        
        // ML -> DL
        addKnowledgeRelation("ml-001", "dl-001");
    }
    
    @Override
    protected void handleKnowledgeQuery(KnowledgeQuery query) {
        log.info("处理知识查询: {}", query.getTopic());
        
        // 执行智能检索
        List<KnowledgeItem> results = performIntelligentSearch(query);
        
        // 更新访问统计
        results.forEach(item -> accessCounts.merge(item.getId(), 1, Integer::sum));
        
        // 创建响应
        KnowledgeResponse response = new KnowledgeResponse(query.getTopic(), results);
        response.setTotalFound(results.size());
        response.setAvgRelevance(results.stream()
            .mapToDouble(KnowledgeItem::getRelevance)
            .average().orElse(0.0));
        
        sender.tell(response, self);
        
        log.info("知识查询完成，返回 {} 个结果", results.size());
    }
    
    @Override
    protected void handleKnowledgeUpdate(KnowledgeUpdate update) {
        log.info("处理知识更新: {} - {}", update.getOperation(), update.getItem().getTitle());
        
        switch (update.getOperation()) {
            case CREATE -> addKnowledge(update.getItem());
            case UPDATE -> updateKnowledge(update.getItem());
            case DELETE -> deleteKnowledge(update.getItem().getId());
            case MERGE -> mergeKnowledge(update.getItem());
        }
        
        sender.tell("知识更新完成: " + update.getOperation(), self);
    }
    
    /**
     * 执行智能检索
     */
    private List<KnowledgeItem> performIntelligentSearch(KnowledgeQuery query) {
        List<KnowledgeItem> candidates = new ArrayList<>();
        
        // 1. 基于主题的精确匹配
        candidates.addAll(searchByTopic(query.getTopic()));
        
        // 2. 基于关键词的模糊匹配
        if (query.getKeywords() != null) {
            candidates.addAll(searchByKeywords(query.getKeywords()));
        }
        
        // 3. 基于领域的分类检索
        if (query.getDomain() != null) {
            candidates.addAll(searchByDomain(query.getDomain()));
        }
        
        // 4. 去重并计算相关度
        Map<String, KnowledgeItem> uniqueResults = new HashMap<>();
        for (KnowledgeItem item : candidates) {
            String id = item.getId();
            if (!uniqueResults.containsKey(id)) {
                // 计算相关度
                double relevance = calculateRelevance(item, query);
                item.setRelevance(relevance);
                uniqueResults.put(id, item);
            }
        }
        
        // 5. 过滤和排序
        return uniqueResults.values().stream()
            .filter(item -> item.getRelevance() >= query.getMinRelevance())
            .sorted((a, b) -> Double.compare(b.getRelevance(), a.getRelevance()))
            .limit(query.getMaxResults())
            .collect(Collectors.toList());
    }
    
    /**
     * 基于主题搜索
     */
    private List<KnowledgeItem> searchByTopic(String topic) {
        return knowledgeBase.values().stream()
            .filter(item -> item.getTitle().toLowerCase().contains(topic.toLowerCase()) ||
                           item.getContent().toLowerCase().contains(topic.toLowerCase()))
            .collect(Collectors.toList());
    }
    
    /**
     * 基于关键词搜索
     */
    private List<KnowledgeItem> searchByKeywords(List<String> keywords) {
        Set<String> resultIds = new HashSet<>();
        
        for (String keyword : keywords) {
            Set<String> ids = keywordIndex.get(keyword.toLowerCase());
            if (ids != null) {
                resultIds.addAll(ids);
            }
        }
        
        return resultIds.stream()
            .map(knowledgeBase::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    /**
     * 基于领域搜索
     */
    private List<KnowledgeItem> searchByDomain(String domain) {
        Set<String> ids = domainIndex.get(domain);
        if (ids == null) return Collections.emptyList();
        
        return ids.stream()
            .map(knowledgeBase::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    /**
     * 计算相关度
     */
    private double calculateRelevance(KnowledgeItem item, KnowledgeQuery query) {
        double relevance = 0.0;
        
        // 标题匹配权重：40%
        if (item.getTitle().toLowerCase().contains(query.getTopic().toLowerCase())) {
            relevance += 0.4;
        }
        
        // 内容匹配权重：30%
        if (item.getContent().toLowerCase().contains(query.getTopic().toLowerCase())) {
            relevance += 0.3;
        }
        
        // 关键词匹配权重：20%
        if (query.getKeywords() != null) {
            long matchingKeywords = query.getKeywords().stream()
                .mapToLong(keyword -> item.getTags().stream()
                    .mapToLong(tag -> tag.toLowerCase().contains(keyword.toLowerCase()) ? 1 : 0)
                    .sum())
                .sum();
            relevance += 0.2 * (double) matchingKeywords / query.getKeywords().size();
        }
        
        // 领域匹配权重：10%
        if (query.getDomain() != null && query.getDomain().equals(item.getDomain())) {
            relevance += 0.1;
        }
        
        // 质量分数调整
        Double qualityScore = qualityScores.get(item.getId());
        if (qualityScore != null) {
            relevance *= qualityScore;
        }
        
        // 访问热度调整
        Integer accessCount = accessCounts.get(item.getId());
        if (accessCount != null && accessCount > 0) {
            relevance *= (1.0 + Math.log(accessCount) * 0.1);
        }
        
        return Math.min(1.0, relevance);
    }
    
    /**
     * 添加知识
     */
    private void addKnowledge(KnowledgeItem item) {
        knowledgeBase.put(item.getId(), item);
        
        // 更新索引
        updateDomainIndex(item);
        updateKeywordIndex(item);
        
        // 计算质量分数
        qualityScores.put(item.getId(), calculateQualityScore(item));
        
        log.debug("添加知识: {} - {}", item.getId(), item.getTitle());
    }
    
    /**
     * 更新知识
     */
    private void updateKnowledge(KnowledgeItem item) {
        KnowledgeItem existing = knowledgeBase.get(item.getId());
        if (existing != null) {
            // 移除旧索引
            removeDomainIndex(existing);
            removeKeywordIndex(existing);
        }
        
        // 更新知识项
        item.setLastUpdated(LocalDateTime.now());
        knowledgeBase.put(item.getId(), item);
        
        // 重建索引
        updateDomainIndex(item);
        updateKeywordIndex(item);
        
        // 重新计算质量分数
        qualityScores.put(item.getId(), calculateQualityScore(item));
        
        log.debug("更新知识: {} - {}", item.getId(), item.getTitle());
    }
    
    /**
     * 删除知识
     */
    private void deleteKnowledge(String id) {
        KnowledgeItem item = knowledgeBase.remove(id);
        if (item != null) {
            removeDomainIndex(item);
            removeKeywordIndex(item);
            qualityScores.remove(id);
            accessCounts.remove(id);
            
            // 移除关联关系
            knowledgeGraph.remove(id);
            knowledgeGraph.values().forEach(relations -> relations.remove(id));
            
            log.debug("删除知识: {} - {}", id, item.getTitle());
        }
    }
    
    /**
     * 合并知识
     */
    private void mergeKnowledge(KnowledgeItem newItem) {
        // 简化的合并逻辑：如果存在则更新，否则创建
        if (knowledgeBase.containsKey(newItem.getId())) {
            updateKnowledge(newItem);
        } else {
            addKnowledge(newItem);
        }
    }
    
    /**
     * 更新领域索引
     */
    private void updateDomainIndex(KnowledgeItem item) {
        domainIndex.computeIfAbsent(item.getDomain(), k -> ConcurrentHashMap.newKeySet())
            .add(item.getId());
    }
    
    /**
     * 更新关键词索引
     */
    private void updateKeywordIndex(KnowledgeItem item) {
        for (String tag : item.getTags()) {
            keywordIndex.computeIfAbsent(tag.toLowerCase(), k -> ConcurrentHashMap.newKeySet())
                .add(item.getId());
        }
    }
    
    /**
     * 移除领域索引
     */
    private void removeDomainIndex(KnowledgeItem item) {
        Set<String> ids = domainIndex.get(item.getDomain());
        if (ids != null) {
            ids.remove(item.getId());
        }
    }
    
    /**
     * 移除关键词索引
     */
    private void removeKeywordIndex(KnowledgeItem item) {
        for (String tag : item.getTags()) {
            Set<String> ids = keywordIndex.get(tag.toLowerCase());
            if (ids != null) {
                ids.remove(item.getId());
            }
        }
    }
    
    /**
     * 计算知识质量分数
     */
    private double calculateQualityScore(KnowledgeItem item) {
        double score = 0.5; // 基础分数
        
        // 内容长度评分
        if (item.getContent().length() > 100) score += 0.2;
        if (item.getContent().length() > 500) score += 0.1;
        
        // 标签数量评分
        if (item.getTags().size() >= 3) score += 0.1;
        if (item.getTags().size() >= 5) score += 0.1;
        
        // 来源可信度评分
        if (item.getSource() != null && !item.getSource().isEmpty()) {
            score += 0.1;
        }
        
        return Math.min(1.0, score);
    }
    
    /**
     * 添加知识关联
     */
    private void addKnowledgeRelation(String fromId, String toId) {
        knowledgeGraph.computeIfAbsent(fromId, k -> ConcurrentHashMap.newKeySet()).add(toId);
        knowledgeGraph.computeIfAbsent(toId, k -> ConcurrentHashMap.newKeySet()).add(fromId);
    }
    
    /**
     * 创建知识项
     */
    private KnowledgeItem createKnowledgeItem(String id, String title, String content,
                                            String domain, List<String> tags, String source) {
        KnowledgeItem item = new KnowledgeItem();
        item.setId(id);
        item.setTitle(title);
        item.setContent(content);
        item.setDomain(domain);
        item.setTags(tags);
        item.setLastUpdated(LocalDateTime.now());
        item.setSource(source);
        return item;
    }
    
    @Override
    protected void processThinkingRequest(ThinkingRequest request) {
        // 知识管理Agent可以协助思考，提供相关知识
        log.info("协助思考请求: {}", request.getQuery());
        
        // 搜索相关知识
        KnowledgeQuery query = new KnowledgeQuery(request.getQuery(), null, null);
        List<KnowledgeItem> relevantKnowledge = performIntelligentSearch(query);
        
        // 构建知识支持的响应
        StringBuilder knowledgeSupport = new StringBuilder();
        knowledgeSupport.append("基于知识库，我找到了以下相关信息：\n\n");
        
        for (KnowledgeItem item : relevantKnowledge.subList(0, Math.min(3, relevantKnowledge.size()))) {
            knowledgeSupport.append("• ").append(item.getTitle()).append(": ")
                .append(item.getContent().substring(0, Math.min(100, item.getContent().length())))
                .append("...\n");
        }
        
        List<ReasoningStep> steps = Arrays.asList(
            new ReasoningStep(1, "知识检索", "搜索相关知识", request.getQuery(),
                "找到了 " + relevantKnowledge.size() + " 个相关知识项", 0.8, 100, null)
        );
        
        ThinkingResponse response = new ThinkingResponse(
            request.getQuery(), knowledgeSupport.toString(), steps, 0.7);
        
        sender.tell(response, self);
    }
    
    @Override
    protected boolean canHandleCollaboration(CollaborationRequest request) {
        return request.getType() == CollaborationType.CONSULTATION ||
               request.getTask().toLowerCase().contains("知识") ||
               request.getTask().toLowerCase().contains("信息") ||
               request.getTask().toLowerCase().contains("查询");
    }
    
    @Override
    protected void processCollaborationRequest(CollaborationRequest request) {
        log.info("处理知识协作请求: {}", request.getTask());
        
        // 将协作任务转换为知识查询
        KnowledgeQuery query = new KnowledgeQuery(request.getTask(), null, null);
        handleKnowledgeQuery(query);
    }
}
