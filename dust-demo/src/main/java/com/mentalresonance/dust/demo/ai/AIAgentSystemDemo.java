package com.mentalresonance.dust.demo.ai;

import com.mentalresonance.dust.core.actors.ActorSystem;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.services.GsonPersistenceService;
import com.mentalresonance.dust.demo.ai.agents.CoordinatorAgent;
import com.mentalresonance.dust.demo.ai.agents.DeepThinkingAgent;
import com.mentalresonance.dust.demo.ai.agents.KnowledgeManagerAgent;
import com.mentalresonance.dust.demo.ai.messages.AIMessages.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Scanner;

/**
 * AI Agent系统演示
 * 
 * 这个演示展示了一个完整的多Agent AI系统，包括：
 * 1. 深度思考Agent：进行复杂推理和分析
 * 2. 知识管理Agent：管理和检索知识
 * 3. 协调Agent：协调多Agent协作
 * 4. 交互式用户界面：支持实时对话
 * 
 * 系统特性：
 * - 多Agent协作：不同Agent专注于不同能力
 * - 智能任务分发：根据任务复杂度选择处理策略
 * - 知识驱动：基于知识库进行推理
 * - 可扩展架构：易于添加新的Agent类型
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class AIAgentSystemDemo {
    
    private ActorSystem system;
    private ActorRef coordinatorAgent;
    private ActorRef deepThinkingAgent;
    private ActorRef knowledgeManagerAgent;
    private Scanner scanner;
    
    public static void main(String[] args) {
        log.info("=== AI Agent系统演示启动 ===");
        
        AIAgentSystemDemo demo = new AIAgentSystemDemo();
        try {
            demo.initialize();
            demo.runInteractiveDemo();
        } catch (Exception e) {
            log.error("演示过程中发生错误", e);
        } finally {
            demo.shutdown();
        }
    }
    
    /**
     * 初始化系统
     */
    private void initialize() throws Exception {
       /* log.info("初始化AI Agent系统...");
        
        // 创建Actor系统
        system = ActorSystem.create("AIAgentSystem", 8080);
        
        // 设置持久化服务
        system.getContext().setPersistenceService(
            GsonPersistenceService.create("./ai-agent-data", "json"));
        
        // 创建各种Agent
        createAgents();
        
        // 等待Agent启动
        Thread.sleep(2000);
        
        // 初始化Agent间的连接
        initializeAgentConnections();
        
        // 预热系统
        warmupSystem();
        
        scanner = new Scanner(System.in);
        
        log.info("AI Agent系统初始化完成！");*/
    }
    
    /**
     * 创建Agent
     */
    private void createAgents() throws Exception {
        log.info("创建AI Agent...");
        
        // 创建深度思考Agent
        deepThinkingAgent = system.getContext().actorOf(
            DeepThinkingAgent.props("DeepThinkingAgent"), "DeepThinkingAgent");
        log.info("深度思考Agent已创建");
        
        // 创建知识管理Agent
        knowledgeManagerAgent = system.getContext().actorOf(
            KnowledgeManagerAgent.props("KnowledgeManagerAgent"), "KnowledgeManagerAgent");
        log.info("知识管理Agent已创建");
        
        // 创建协调Agent
        coordinatorAgent = system.getContext().actorOf(
            CoordinatorAgent.props("CoordinatorAgent"), "CoordinatorAgent");
        log.info("协调Agent已创建");
    }
    
    /**
     * 初始化Agent间的连接
     */
    private void initializeAgentConnections() {
        log.info("初始化Agent间连接...");
        
        // 让协调Agent发现其他Agent
        coordinatorAgent.tell("check-agents", null);
        
        // 等待发现完成
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 预热系统
     */
    private void warmupSystem() {
        log.info("预热AI系统...");
        
        // 发送一些预热请求
        ThinkingRequest warmupRequest = new ThinkingRequest(
            "系统预热测试", "这是一个系统预热请求", ThinkingMode.FAST_RESPONSE);
        coordinatorAgent.tell(warmupRequest, null);
        
        // 测试知识查询
        KnowledgeQuery warmupQuery = new KnowledgeQuery(
            "人工智能", Arrays.asList("AI", "机器学习"), "计算机科学");
        knowledgeManagerAgent.tell(warmupQuery, null);
        
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("系统预热完成");
    }
    
    /**
     * 运行交互式演示
     */
    private void runInteractiveDemo() {
        printWelcomeMessage();
        
        while (true) {
            try {
                System.out.print("\n🤖 请输入您的问题 (输入 'help' 查看帮助, 'quit' 退出): ");
                String input = scanner.nextLine().trim();
                
                if (input.isEmpty()) {
                    continue;
                }
                
                if ("quit".equalsIgnoreCase(input) || "exit".equalsIgnoreCase(input)) {
                    System.out.println("👋 感谢使用AI Agent系统，再见！");
                    break;
                }
                
                if ("help".equalsIgnoreCase(input)) {
                    printHelpMessage();
                    continue;
                }
                
                if (input.startsWith("/")) {
                    handleSystemCommand(input);
                    continue;
                }
                
                // 处理用户问题
                handleUserQuestion(input);
                
            } catch (Exception e) {
                log.error("处理用户输入时发生错误", e);
                System.out.println("❌ 抱歉，处理您的请求时发生了错误。请重试。");
            }
        }
    }
    
    /**
     * 处理用户问题
     */
    private void handleUserQuestion(String question) {
        System.out.println("\n🔍 正在分析您的问题...");
        
        // 分析问题类型并选择合适的处理方式
        ThinkingMode mode = determineThinkingMode(question);
        
        ThinkingRequest request = new ThinkingRequest(question, 
            "用户交互会话", mode);
        request.setMaxDepth(5);
        request.setTimeoutMs(30000L);
        
        System.out.println("🧠 思考模式: " + getThinkingModeDescription(mode));
        System.out.println("⏳ 正在思考中，请稍候...\n");
        
        // 发送给协调Agent处理
        coordinatorAgent.tell(request, null);
        
        // 等待处理完成（简化处理，实际应用中应该异步处理）
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 确定思考模式
     */
    private ThinkingMode determineThinkingMode(String question) {
        String lowerQuestion = question.toLowerCase();
        
        if (lowerQuestion.contains("分析") || lowerQuestion.contains("比较") || 
            lowerQuestion.contains("评估") || lowerQuestion.contains("推理")) {
            return ThinkingMode.ANALYTICAL;
        }
        
        if (lowerQuestion.contains("创新") || lowerQuestion.contains("创造") || 
            lowerQuestion.contains("设计") || lowerQuestion.contains("想象")) {
            return ThinkingMode.CREATIVE;
        }
        
        if (lowerQuestion.contains("批判") || lowerQuestion.contains("质疑") || 
            lowerQuestion.contains("反驳") || lowerQuestion.contains("缺点")) {
            return ThinkingMode.CRITICAL;
        }
        
        if (lowerQuestion.length() > 100 || lowerQuestion.contains("复杂") || 
            lowerQuestion.contains("深入")) {
            return ThinkingMode.DEEP_REASONING;
        }
        
        return ThinkingMode.COLLABORATIVE;
    }
    
    /**
     * 获取思考模式描述
     */
    private String getThinkingModeDescription(ThinkingMode mode) {
        return switch (mode) {
            case ANALYTICAL -> "分析模式 - 逻辑分析和推理";
            case CREATIVE -> "创造模式 - 发散思维和创新";
            case CRITICAL -> "批判模式 - 批判性思维";
            case DEEP_REASONING -> "深度推理 - 复杂问题深度分析";
            case COLLABORATIVE -> "协作模式 - 多Agent协作思考";
            case FAST_RESPONSE -> "快速响应 - 基于经验快速回答";
        };
    }
    
    /**
     * 处理系统命令
     */
    private void handleSystemCommand(String command) {
        String cmd = command.substring(1).toLowerCase();
        
        switch (cmd) {
            case "status" -> {
                System.out.println("\n📊 系统状态:");
                coordinatorAgent.tell("status", null);
                deepThinkingAgent.tell("status", null);
                knowledgeManagerAgent.tell("status", null);
            }
            case "agents" -> {
                System.out.println("\n🤖 已注册的Agent:");
                coordinatorAgent.tell("list-agents", null);
            }
            case "sessions" -> {
                System.out.println("\n💬 活跃会话:");
                coordinatorAgent.tell("active-sessions", null);
            }
            case "knowledge" -> {
                System.out.println("\n📚 测试知识查询:");
                KnowledgeQuery query = new KnowledgeQuery("人工智能", 
                    Arrays.asList("AI", "机器学习"), "计算机科学");
                knowledgeManagerAgent.tell(query, null);
            }
            case "demo" -> runDemoScenarios();
            default -> System.out.println("❌ 未知系统命令: " + command);
        }
        
        // 等待命令执行
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 运行演示场景
     */
    private void runDemoScenarios() {
        System.out.println("\n🎭 运行演示场景...\n");
        
        String[] demoQuestions = {
            "什么是人工智能？它有哪些应用领域？",
            "分析机器学习和深度学习的区别和联系",
            "如何设计一个创新的智能家居系统？",
            "批判性地评估当前AI技术的局限性",
            "深入分析量子计算对人工智能发展的影响"
        };
        
        for (int i = 0; i < demoQuestions.length; i++) {
            System.out.println("📝 演示问题 " + (i + 1) + ": " + demoQuestions[i]);
            handleUserQuestion(demoQuestions[i]);
            
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
            
            System.out.println("─".repeat(80));
        }
        
        System.out.println("✅ 演示场景完成！");
    }
    
    /**
     * 打印欢迎信息
     */
    private void printWelcomeMessage() {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("🚀 欢迎使用 Dust-Core AI Agent 系统！");
        System.out.println("=".repeat(80));
        System.out.println("这是一个基于Actor模型的多Agent AI系统演示。");
        System.out.println("系统包含以下Agent：");
        System.out.println("  🧠 深度思考Agent - 专注于复杂推理和分析");
        System.out.println("  📚 知识管理Agent - 负责知识存储和检索");
        System.out.println("  🎯 协调Agent - 协调多Agent协作");
        System.out.println("\n您可以：");
        System.out.println("  • 提出任何问题，系统会智能选择最佳处理方式");
        System.out.println("  • 使用系统命令（以/开头）查看系统状态");
        System.out.println("  • 输入 'help' 查看详细帮助");
        System.out.println("=".repeat(80));
    }
    
    /**
     * 打印帮助信息
     */
    private void printHelpMessage() {
        System.out.println("\n📖 帮助信息:");
        System.out.println("─".repeat(50));
        System.out.println("💬 普通问题:");
        System.out.println("  直接输入您的问题，系统会自动分析并选择最佳处理方式");
        System.out.println("\n🔧 系统命令 (以/开头):");
        System.out.println("  /status   - 查看系统状态");
        System.out.println("  /agents   - 查看已注册的Agent");
        System.out.println("  /sessions - 查看活跃会话");
        System.out.println("  /knowledge- 测试知识查询");
        System.out.println("  /demo     - 运行演示场景");
        System.out.println("\n🎯 思考模式:");
        System.out.println("  系统会根据问题内容自动选择合适的思考模式：");
        System.out.println("  • 分析模式 - 包含'分析'、'比较'等关键词");
        System.out.println("  • 创造模式 - 包含'创新'、'设计'等关键词");
        System.out.println("  • 批判模式 - 包含'批判'、'质疑'等关键词");
        System.out.println("  • 深度推理 - 复杂或长问题");
        System.out.println("  • 协作模式 - 默认模式，多Agent协作");
        System.out.println("\n⌨️  其他命令:");
        System.out.println("  help/Help - 显示此帮助信息");
        System.out.println("  quit/exit - 退出系统");
        System.out.println("─".repeat(50));
    }
    
    /**
     * 关闭系统
     */
    private void shutdown() {
        log.info("关闭AI Agent系统...");
        
        if (scanner != null) {
            scanner.close();
        }
        
        if (system != null) {
            system.stop();
        }
        
        log.info("AI Agent系统已关闭");
    }
}
