# 灰尘
Java（Java 19 及更高版本）中（非常）虚拟线程的出现使现代 Java 能够创建 100,000 个
或单个可执行文件中的数百万个执行线程。然而，通过设计，管理这些轻量级的框架
线程与 Java 应用程序只能支持更少的数字时相同。

因此，编写正确的高线程程序的问题仍然存在并被放大。

因此，我们有三个选择：
1. 不要利用利用大量线程的可能性。
2. 利用多余的线程，继续以通常的方式解决锁、竞争条件等问题
3. 采用不同的范式来构建程序，可以利用能够使用非常
大量线程，同时避免许多相关问题。

Dust 采取了第三条路线，但使用了一个已有 50 年历史的想法——演员的想法。

Actor 是一个封闭的对象，它与外部世界通信的唯一形式是通过
与其他Actor交换消息。在内部，Actor响应特定消息时执行的作是
调用其行为，并且Actor可以更改其行为以响应它收到的消息。
*重要*Actor本身是单线程的，并按照以下顺序一次处理一条消息
他们收到了。

Actor 之外的世界通常填充了（可能数百万个）其他 Actor，并且
应用程序创建是定义Actor类的行为和传递的消息的过程
他们之间。

Actors 实现并不新鲜 - Erland 和 Elixir 语言是受 Actor 启发的语言，它们运行在
专门为支持 Actor 范式而设计的虚拟机，Akka 是 Actor 系统的另一个例子
构建在 JVM 之上。事实上，Dust 从中汲取了一些灵感
来自 Akka 的“经典”演员，所以如果你熟悉 Akka，你应该会发现 Dust 非常容易
来掌握。

## 惯用灰尘
Dust 以 Actor 概念为基础，通过为简单但强大的成语提供支持来扩展它，这些成语自然会“脱落”。
实施。这些包括：
* 管道 -- 动态创建 Actor 链或图表，这些 Actor 合作解决更复杂的处理任务
* ServiceManagers -- 动态创建相同 Actor 的池以扩展（或限制）处理
* 委托模型 -- Actor 动态地将他们的消息传递给其他可能更专业的 Actor，然后
在特定事件发生时重新获得控制权
* 收获 -- 从大家族的 Actor 中“汇总”信息
* 实体 -- 支持对“真实世界”对象及其关系进行建模的持久化 Actor。
因此，Dust 直接支持 ..
* 基于事件的数字孪生 - 对“现实世界”事件和实体进行建模的 Actor 系统。

## 部分应用
Dust（或其前身）已被用于创建多种不同的应用，包括：
* 一个癌症治疗推荐平台，使用 dust-nlp 库来摄取和构建医疗记录和
将它们与其他信息来源（临床试验、期刊等）整合。
* 商业新闻监控平台。它监视和分析 100 多个新闻提要的内容，并创建基于 Actor
商业实体的孪生，并监控它们之间的趋势事件（合并、产品公告、诉讼等）。
* wifi监控平台。Raspberry Pi 托管的 Actor 网络创建，用于孪生附近的 wifi 客户端。
这些Actor将信息“汇总”到基于服务器的Actor，这些Actor与物理工厂（例如无线接入点、房间）成对
在建筑物中）并使用基于信号强度的三角测量给出了有多少人在哪里的粗略模型（这是在 Covid 高峰期）
* <img src=“./dustville.png” alt=“图像描述” width=“300px” style=“float：left; margin-right： 10px; vertical-align：middle”>
Dustville（包含在 dust-core 中）- 一个玩具数字孪生示例，它模拟了一个有汽车、充电站、
房屋，以及一个发电站，周围有椋鸟的低语声（因为，当然，我们可以）。
没有实际用途，但是一个有趣的玩具。

<div style=“clear：left”></div>

## 许可证
Dust 根据 Apache 2.0 开源许可证获得许可。

## 库
完整的（到目前为止）Dust 库系列包括：
1. dust-core - 这个库。完全支持本地和远程 Actor 以及许多 Dusty 习语
2. dust-http - 支持集成 http 和 Actor 消息传递。
3. dust-html - 支持创建执行 html 文档处理的 Actor 管道
4. dust-feeds - 用于创建各种提要的库 - RSS 客户端、Web 爬虫、搜索引擎（通过 SearxNG）
5. dust-nlp - LLM 和 Spacey 通过允许创建 NLP 管道的 Actors 支持

## 开始使用
1. 确保您安装了 Java 21 或更高版本并安装了 Gradle
2. git 克隆 https://github.com/dust-ai-mr/dust-core.git
3. CD尘芯
4. ./gradlew clean
5. ./gradlew 测试
6. ./gradlew publishMavenLocal

## 示例
* dust-ville 项目 - 使用 Actors 进行数字孪生的简单示例;在这种情况下，一个简单的小镇，有房屋，街道上有可充电电动汽车，一个为房屋供暖和为汽车充电的发电站，还有一群椋鸟在发电站周围翱翔。仅鸟类模拟就创建了8000多个Actor。

* 测试目录包含许多支持的 Dust 习语的测试，是对 Dust 基础知识的良好介绍。

* dust-nlp 中的新闻阅读器迷你应用程序。自动查找与给定主题相关的新 RSS 提要，
实时监控这些提要以查找主题匹配的文章并生成文章摘要。

## 支持/应用程序开发
联系 <EMAIL>