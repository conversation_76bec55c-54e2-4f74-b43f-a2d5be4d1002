# Release notes for Dust v 1.1.5

For details see commit logs

1. Numerous bug fixes from 1.0.x. version
2. Better exception handling within Actors - Actors and their descendants can find out what exception caused their interruption and handle accordingly.
3. Added Jackson Actor persistence to FST and Gson. <PERSON> can be more forgiving for development
4. Added some more idioms to the Actor library
5. 