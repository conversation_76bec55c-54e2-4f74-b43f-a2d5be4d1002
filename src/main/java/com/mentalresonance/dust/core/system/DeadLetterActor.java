/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.system;

import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.actors.lib.PubSubActor;
import com.mentalresonance.dust.core.msgs.DeadLetter;
import com.mentalresonance.dust.core.msgs.PubSubMsg;
import com.mentalresonance.dust.core.msgs.ZombieMsg;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.LinkedList;

/**
 * 死信Actor：处理发送给不存在Actor的消息
 *
 * 当消息被发送到不存在的Actor时，这些消息会被重定向到DeadLetterActor。
 * 消息会被记录，并且可以通知其他感兴趣的Actor。
 *
 * DeadLetterActor是一个发布/订阅{@link PubSubActor}Actor，因此其他Actor
 * 可以订阅以获得投递失败的通知。但与常规的发布/订阅Actor不同，
 * 这个Actor会将订阅的消息包装在DeadLetter中。
 *
 * 主要功能：
 * 1. 接收和记录死信消息
 * 2. 向订阅者分发死信通知
 * 3. 提供死信的统计和监控
 * 4. 支持死信的过滤和处理
 *
 * DeadLetterActor在ActorSystem创建时自动创建，可以通过
 * context.getDeadLetterActor()获取。
 */
@Slf4j
public class DeadLetterActor extends PubSubActor {

    /**
     * 父类行为：保存PubSubActor的原始行为处理器
     */
    ActorBehavior parentBehavior;

    /**
     * 死信日志标志：控制是否记录死信到日志
     */
    final boolean logDeadLetters;

    /**
     * 创建Props配置对象
     *
     * @param logDeadLetters 如果为true则记录死信日志
     * @return Props配置对象
     */
    public static Props props(Boolean logDeadLetters) {
        return Props.create(DeadLetterActor.class, logDeadLetters);
    }

    /**
     * 构造函数：创建死信Actor实例
     *
     * @param logDeadLetters 如果为true则记录死信日志
     */
    public DeadLetterActor(Boolean logDeadLetters) {
        this.logDeadLetters = logDeadLetters;
    }

    /**
     * 启动前初始化：保存父类的行为处理器
     */
    @Override
    protected void preStart() {
        parentBehavior = super.createBehavior();
    }

    /**
     * 停止后清理：记录死信Actor关闭信息
     */
    @Override
    protected void postStop() {
        log.info("/system/deadletters 已关闭");
    }

    /**
     * 创建默认行为：处理死信和发布订阅消息
     *
     * @return 消息处理行为
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch(message)
            {
                case PubSubMsg pubsub -> {
                    // 委托给父类处理发布订阅消息
                    parentBehavior.onMessage(pubsub);
                }

                case DeadLetter dl -> {
                    boolean hasRegistrants = false;
                    // 检查是否有订阅者对此类型的死信感兴趣
                    HashMap<String, ActorRef> registrants = subs.get(message.getClass().getName());

                    log.trace("处理死信：{}", dl.toString());

                    if (null != registrants) {
                        hasRegistrants = true;
                        // 向所有订阅者发布死信消息
                        _Publish publish = new _Publish(null, dl, new LinkedList<>(registrants.values().stream().toList()));
                        self.tell(publish, self);
                    }

                    // 如果启用日志记录且没有订阅者且不是僵尸消息，则记录警告
                    if (logDeadLetters && (!hasRegistrants) && !(dl.getMessage() instanceof ZombieMsg)) {
                        log.warn("死信：消息{}从{}发送到{} [有订阅者={}]", dl.getMessage(), dl.getSender(), dl.getPath(), hasRegistrants);
                    }

                }

                case _Publish pub -> parentBehavior.onMessage(pub);

                default ->  log.error(String.format("Dead Letter actor got message %s", message));

            }
        };
    }
}
