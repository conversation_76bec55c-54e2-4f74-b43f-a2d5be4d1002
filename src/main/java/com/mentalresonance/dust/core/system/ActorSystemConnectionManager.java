/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.system;

import com.mentalresonance.dust.core.services.SerializationService;
import lombok.extern.slf4j.Slf4j;
import org.nustaq.net.TCPObjectSocket;

import java.io.IOException;
import java.net.Socket;
import java.net.URI;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * Actor系统连接管理器：维护从本地Actor系统到远程Actor系统的连接池
 *
 * 动态创建连接（固定池大小为SocketsPerRemote），否则如果每次tell()都要
 * 打开/关闭连接，性能会很糟糕。
 *
 * 主要功能：
 * 1. 连接池管理：维护到远程系统的TCP连接池
 * 2. 连接复用：避免频繁创建和销毁连接
 * 3. 健康检查：定期检查连接状态并清理失效连接
 * 4. 并发安全：支持多线程并发访问
 * 5. 资源管理：自动清理和回收连接资源
 */
@Slf4j
public class ActorSystemConnectionManager {

    /**
     * 套接字锁：用于同步套接字操作的锁对象
     */
    private static final Object SocketLock = new Object();

    /**
     * 每个远程系统的套接字数量：连接池的大小限制
     */
    private static final Integer SocketsPerRemote = 16;

    /**
     * Ping间隔：检查连接状态的时间间隔（15秒）
     */
    final long PING_INTERVAL = 15000;

    /**
     * Ping超时：如果超过此时间没有收到连接响应则清理连接（30秒）
     */
    final long PING_TIMEOUT = 30000;

    /**
     * 管理线程：负责定期清理失效连接的后台线程
     */
    final Thread managerThread;

    /**
     * 远程套接字映射：当对象服务器接收到新连接时，将其套接字放在这里
     * 这意味着如果停止时我们可以关闭我们这端的连接
     */
    private final ConcurrentHashMap<Socket, Boolean> remoteSockets = new ConcurrentHashMap<>();

    /**
     * 构造函数：创建连接管理器并启动管理线程
     *
     * 远程系统可能会宕机，但我们的套接字不知道。所以我们定期检查
     * 是否收到了系统的响应 - 如果没有，我们移除连接并等待再次收到响应。
     */
    public ActorSystemConnectionManager() {
        managerThread = Thread.startVirtualThread(() -> {
            boolean running = true;
            while (running) {
                try {
                    Thread.sleep(PING_INTERVAL);
                    flushPool(false); // 定期清理连接池
                }
                catch (InterruptedException e) {
                    running = false; // 被中断时停止运行
                }
            }
            log.info("连接池管理器已关闭");
        });
    }

    /**
     * 远程Actor系统连接池：保存到host:port的预备连接列表
     *
     * 映射结构：Hash(remote-host, remote-port, remote system) -> 到远程系统的连接池
     *
     * 由于我们希望将套接字返回到这些池中，我们需要确保基于主机和端口查询套接字的键
     * 是完全可靠/可逆的。遗憾的是，据我所知这并不总是如此，因为我们往往会遇到
     * localhost - 127.0.0.1 冲突等问题。所以我们将套接字包装在一个包含存储键的类中，
     * 这样当我们返回它时就没有疑问了。
     */
    final ConcurrentHashMap<String, ConnectionPool> remoteActorSystems = new ConcurrentHashMap<>();

    /**
     * 从路径获取远程系统的键
     *
     * @param path URI路径
     * @return 格式化的主机:端口键
     */
    private String remoteKey(URI path) {
        return "%s:%d".formatted(path.getHost(), path.getPort());
    }

    /**
     * Flush the socket pool
     * @param all if true the completely drain the pool, otherwise only those who haven't been pinged recently
     */
    public void flushPool(boolean all) {
        synchronized (SocketLock) {
            for (String key : remoteActorSystems.keySet()) {
                try {
                    ConnectionPool pool = remoteActorSystems.get(key);
                    pool.flush(all);
                } catch (Exception e) {
                    log.error("flushPool(): %s".formatted(e.getMessage()));
                }
            }
        }
    }

    /**
     * Get a wrapped socket to the given uri
     * @param uri target uri
     * @return The wrapped socket object
     * @throws IOException on errors
     * @throws InterruptedException if interrupted
     */
    public WrappedTCPObjectSocket getSocket(URI uri) throws IOException, InterruptedException {
        String key = remoteKey(uri);
        ConnectionPool pool;
        int retries = 5;

        while (--retries >= 0) {
            try {
                synchronized (SocketLock) {
                    if (!remoteActorSystems.containsKey(key)) {
                        pool = new ConnectionPool(SocketsPerRemote, key, uri.getHost(), uri.getPort());
                        remoteActorSystems.put(key, pool);
                    }
                }
                return remoteActorSystems.get(key).acquire(uri.getPath());
            }
            catch (IOException e) {
                log.warn("Failed to get connection to: {}. Retrying.", uri);
                pool = remoteActorSystems.remove(key);
                if (pool != null)
                    try {
                        pool.flush(true);
                    } catch (Exception ignored) {}
                Thread.sleep(3000L);
            }
        }
        log.error("Cannot get connection to remote actor system: {}", uri);
        throw new IOException();
    }

    /**
     * Add incoming connection to managed list
     * @param socket of incoming connection
     */
    public void addRemoteSocket(Socket socket) {
        remoteSockets.put(socket, true);
    }

    /**
     * Close incoming connection. We send a null message so the client knows to close his end.
     * @param socket - socket to close
     */
    public void closeRemoteSocket(Socket socket)  {
        remoteSockets.remove(socket);
        /*
         * If the 'remote' socket was actually the server shutdown null message sender then it has already been
         * closed by the handler.
         */
        try {
            TCPObjectSocket remoteSocket = new TCPObjectSocket(socket, SerializationService.getFstConfiguration());
            remoteSocket.writeObject(null);
            remoteSocket.flush();
            remoteSocket.close();
        }
        catch(Exception ignored) {}
    }

    private void closeRemoteSockets() throws Exception {
        for(Socket socket: remoteSockets.keySet()) {
            closeRemoteSocket(socket);
        }
    }

    /**
     * Shutdown the manager. We close outgoing and incoming connections by sending
     * null messages on them and then closing the sockets.
     *
     * @throws Exception on error
     */
    public void shutdown() throws Exception {
        managerThread.interrupt();
        flushPool(true);
        closeRemoteSockets();
        log.info("Shutdown");
    }

    /**
     * Return wrapped socket to the pool
     * @param objectSocket the socket
     */
    public void returnSocket(WrappedTCPObjectSocket objectSocket) {
        // log.trace("Returning socket");
        ConnectionPool pool = remoteActorSystems.get(objectSocket.key);
        if (pool != null) {
            pool.restore(objectSocket);
        } else
            log.warn("Returning socket to unknown pool: {}", objectSocket.key);
    }

    /**
     * Flush connection pool of all sockets associate with remote ActorSystem of uri
     * @param uri
     * @throws Exception
     */
    public void flushPool(URI uri) throws Exception {
        // log.trace("Returning socket");
        ConnectionPool pool = remoteActorSystems.get(remoteKey(uri));
        if (pool != null) {
            pool.flush(true);
        } else
            log.warn("Flushing unknown pool for: {}", uri);
    }

    /**
     * Takes a TCPObject and associates it with a key
     */
    public static class WrappedTCPObjectSocket {
        final String key;
        public String path; // To Actor when the Socket is used
        /**
         * The TCPObjectSocket
         */
        public final TCPObjectSocket tcpObjectSocket;

        /**
         * Constructor
         * @param key to associate with the socket
         * @param tcpObjectSocket .. socket
         */
        public WrappedTCPObjectSocket(String key, TCPObjectSocket tcpObjectSocket) {
            this.key = key;
            this.tcpObjectSocket = tcpObjectSocket;

        }

    }

    /**
     * Pool of connections fdr one remote ActorSystem. But we have to be careful - we could send two messages to
     * the same remote Actor over two sockets and they could arrive out of order.
     *
     */
    private class ConnectionPool {
        String key;
        long lastPing;
        LinkedBlockingQueue<WrappedTCPObjectSocket> q;
        ConcurrentHashMap<String, Object> resourceLocks = new ConcurrentHashMap<>(SocketsPerRemote);
        ConcurrentHashMap<String, Queue<Thread>> waiting = new ConcurrentHashMap<>();

        ConnectionPool(int size, String key, String host, int port) throws IOException {
            this.key = key;
            log.trace("Creating new pool for: {}", key);
            q = new LinkedBlockingQueue<>();

            for (int i = 0; i < SocketsPerRemote; ++i) {
                q.add(
                    new WrappedTCPObjectSocket(
                        key,
                        new TCPObjectSocket(host, port, SerializationService.getFstConfiguration())
                    )
                );
            }
            lastPing = System.currentTimeMillis();
        }

        private Object getLock(String key) {
            return resourceLocks.computeIfAbsent(key, k -> new Object());
        }

        WrappedTCPObjectSocket acquire(String path) throws IOException, InterruptedException {
            Object lock = getLock(path);
            synchronized (lock) {
                Queue<Thread> queue = waiting.computeIfAbsent(path, k -> new LinkedBlockingQueue<>());
                Thread currentThread = Thread.currentThread();
                queue.add(currentThread);

                // Wait until I am at the head of the queue but do not pop me
                while (queue.peek() != currentThread) {
                    lock.wait();
                }
                WrappedTCPObjectSocket socket =  q.take();
                socket.path = path;
                return socket;
            }
        }

        void restore(WrappedTCPObjectSocket objectSocket) {
            Object lock = getLock(objectSocket.path);
            synchronized (lock) {
                Queue<Thread> queue = waiting.get(objectSocket.path); // I know I am still here on the head
                if (queue != null){
                    queue.remove(); // So remove me
                    q.add(objectSocket); // Return socket
                    if (!queue.isEmpty()) { // An notify the next waitee or dump the queue
                        lock.notifyAll();
                    } else
                        waiting.remove(objectSocket.path);
                } else
                    log.warn("Returning socket to pool with no queue - pool:{}, socketkey: {}", key, objectSocket.key);
            }
        }

        public void flush(boolean all) throws Exception {
            if (all || System.currentTimeMillis() - lastPing > PING_TIMEOUT) {
                log.trace("Flushing pool for key: %s".formatted(key));
                for (WrappedTCPObjectSocket socket : q) {
                    try {
                        if (! socket.tcpObjectSocket.isClosed()) {
                            socket.tcpObjectSocket.writeObject(null);
                            socket.tcpObjectSocket.flush();
                            socket.tcpObjectSocket.getSocket().close();
                        } else {
                            log.warn("Socket to remote actor system {} was closed", key);
                            q.remove(socket);
                        }
                    } catch(Exception ignored) {}
                }
                remoteActorSystems.remove(key);
            }
        }
    }
}
