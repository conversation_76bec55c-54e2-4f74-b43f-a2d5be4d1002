/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.system.exceptions;

/**
 * Actor选择异常：在ActorSelection创建过程中发生的异常
 *
 * 此异常在以下情况下可能被抛出：
 * 1. Actor路径格式不正确
 * 2. 指定的Actor路径不存在
 * 3. 路径解析过程中发生错误
 * 4. 权限不足无法访问指定Actor
 * 5. 网络连接问题（远程Actor选择时）
 *
 * ActorSelection是一种灵活的Actor查找机制，支持通配符和路径模式匹配。
 *
 * <AUTHOR>
 */
public class ActorSelectionException extends Exception {

    /**
     * 默认构造函数：创建不带消息的异常
     */
    public ActorSelectionException() {
        super();
    }

    /**
     * 带消息的构造函数：创建包含错误描述的异常
     *
     * @param msg 异常消息，描述具体的选择错误原因
     */
    public ActorSelectionException(String msg) {
        super(msg);
    }
}
