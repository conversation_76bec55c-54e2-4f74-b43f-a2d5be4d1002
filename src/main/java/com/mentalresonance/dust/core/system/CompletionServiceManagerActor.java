/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.system;

import com.mentalresonance.dust.core.actors.lib.CompletionServiceActor;
import com.mentalresonance.dust.core.actors.lib.ServiceManagerActor;

/**
 * Identity wrapper around service manager for {@link CompletionServiceActor}
 *
 * <AUTHOR>
 */
public class CompletionServiceManagerActor extends ServiceManagerActor {

    /**
     * Constructor
     * @param maxWorkers max workers for this service
     */
    public CompletionServiceManagerActor(Integer maxWorkers) {
        super(CompletionServiceActor.props(), maxWorkers);
    }
}
