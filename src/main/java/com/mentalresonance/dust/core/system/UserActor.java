/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.system;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.Props;
import lombok.extern.slf4j.Slf4j;


/**
 * 用户Actor：根'/'下的两个顶级Actor之一
 *
 * UserActor是Actor层次结构中的重要组件，位于'/user'路径下。
 * 所有非系统Actor都在/user下创建，与之相对的是/system，
 * 后者管理所有系统设置的Actor（参见{@link SystemActor}）。
 *
 * 主要职责：
 * 1. 作为所有用户创建Actor的父节点
 * 2. 提供用户Actor的命名空间隔离
 * 3. 管理用户Actor的生命周期
 * 4. 实现用户Actor的监督策略
 *
 * 这种设计确保了系统Actor和用户Actor的清晰分离，
 * 便于管理和维护不同类型的Actor。
 *
 * <AUTHOR>
 */
@Slf4j
public class UserActor extends Actor {

    /**
     * 创建Props配置对象
     *
     * @return UserActor的Props配置
     */
    public static Props props() {
        return Props.create(UserActor.class);
    }

    /**
     * 默认构造函数：创建UserActor实例
     */
    public UserActor() {}

    /**
     * UserActor停止后的清理工作
     */
    @Override
    protected void postStop() {
        log.info("/user 已关闭");
    }
}
