/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.system;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.actors.SupervisionStrategy;
import com.mentalresonance.dust.core.actors.lib.PubSubActor;
import lombok.extern.slf4j.Slf4j;

/**
 * 系统Actor：管理死信和事件Actor
 *
 * SystemActor是Actor系统中的核心系统组件，负责管理系统级的服务Actor。
 * 它在'/system'路径下运行，主要职责包括：
 * 1. 管理死信处理Actor（DeadLetterActor）
 * 2. 管理事件发布订阅Actor（PubSubActor）
 * 3. 提供系统级的监督策略
 *
 * 这些系统Actor为整个Actor系统提供基础设施服务，
 * 确保消息的可靠传递和事件的正确分发。
 *
 * <AUTHOR>
 */
@Slf4j
public class SystemActor extends Actor {

    /**
     * 死信Actor的名称常量
     */
    public static final String DEAD_LETTERS = "deadletters";

    /**
     * 事件Actor的名称常量
     */
    public static final String EVENTS = "events";

    /**
     * 死信日志标志：控制是否记录死信日志
     */
    final boolean logDeadLetters;

    /**
     * 创建Props配置对象
     *
     * @param logDeadLetters 如果为true则记录死信日志
     * @return SystemActor的Props配置
     */
    public static Props props(Boolean logDeadLetters) {
        return Props.create(SystemActor.class, logDeadLetters);
    }

    /**
     * 构造函数：创建SystemActor实例
     *
     * @param logDeadLetters 如果为true则记录死信日志
     */
    public SystemActor(Boolean logDeadLetters) {
        this.logDeadLetters = logDeadLetters;
    }

    /**
     * SystemActor停止后的清理工作
     */
    @Override
    protected void postStop() {
        log.info("/system 已关闭");
    }

    /**
     * SystemActor启动前的初始化工作
     *
     * 创建系统级的服务Actor并设置监督策略
     */
    @Override
    public void preStart() throws Exception {
        // 设置重启监督策略，一对一模式
        supervisor = new SupervisionStrategy(SupervisionStrategy.SS_RESTART, SupervisionStrategy.MODE_ONE_FOR_ONE);

        // 创建死信处理Actor
        actorOf(DeadLetterActor.props(logDeadLetters), DEAD_LETTERS);

        // 创建事件发布订阅Actor
        actorOf(PubSubActor.props(), EVENTS);
    }
}
