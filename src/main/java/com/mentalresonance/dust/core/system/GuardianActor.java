/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.system;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.SupervisionStrategy;
import com.mentalresonance.dust.core.system.exceptions.ActorInstantiationException;
import lombok.extern.slf4j.Slf4j;

/**
 * Guardian Actor（守护Actor）：由初始路径"/"表示的根Actor
 *
 * Guardian Actor的职责是创建Actor系统的基础层次结构：
 * 1. /user - 所有用户创建的Actor树都在此路径下
 * 2. /system - 系统生成的工具Actor（如死信处理器）都在此路径下
 *
 * Guardian Actor参与系统启动过程，因此在部署方式上需要打破一些常规规则。
 * 特别是我们不能依赖preStart()来完成工作，因为此时系统尚未完全初始化。
 * 因此我们调用init()方法来完成初始化。
 *
 * Guardian Actor是整个Actor层次结构的根节点，负责：
 * - 系统级Actor的创建和管理
 * - 顶层监督策略的实施
 * - 系统关闭时的协调工作
 *
 * <AUTHOR>
 */
@Slf4j
public class GuardianActor extends Actor {

    /**
     * 默认构造函数：创建Guardian Actor实例
     */
    public GuardianActor() {}

    /**
     * 初始化Guardian Actor，创建/system和/user子Actor
     *
     * 这个方法在系统启动时被调用，负责建立基础的Actor层次结构。
     * 设置重启监督策略，确保系统Actor在出现问题时能够自动恢复。
     *
     * @param logDeadLetters 如果为true，系统死信Actor将记录死信日志
     * @throws ActorInstantiationException 创建Actor时发生错误
     */
    public void init(boolean logDeadLetters) throws ActorInstantiationException {
        // 设置监督策略：重启模式，一对一策略（只重启出错的Actor）
        supervisor = new SupervisionStrategy(SupervisionStrategy.SS_RESTART, SupervisionStrategy.MODE_ONE_FOR_ONE);

        // 创建系统Actor，负责系统级服务
        actorOf(SystemActor.props(logDeadLetters), "system");

        // 创建用户Actor，作为所有用户Actor的父节点
        actorOf(UserActor.props(), "user");
    }

    /**
     * Guardian Actor停止后的清理工作
     *
     * 当Guardian Actor停止时，意味着整个Actor系统正在关闭。
     */
    @Override
    protected void postStop() {
        log.info("Guardian Actor已关闭");
    }

}
