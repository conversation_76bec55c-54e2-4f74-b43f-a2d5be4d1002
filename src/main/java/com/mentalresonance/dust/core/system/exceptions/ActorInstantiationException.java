/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.system.exceptions;

/**
 * Actor实例化异常：当Actor创建出错时抛出
 *
 * 此异常在以下情况下可能被抛出：
 * 1. Actor类无法找到或加载
 * 2. Actor构造函数参数不匹配
 * 3. Actor构造函数执行时发生异常
 * 4. 反射操作失败
 * 5. 安全权限不足
 *
 * 这是一个检查异常，调用者必须处理或声明抛出。
 *
 * <AUTHOR>
 */
public class ActorInstantiationException extends Exception {
    /**
     * 默认构造函数：创建不带消息的异常
     */
    public ActorInstantiationException() {}

    /**
     * 带消息的构造函数：创建包含错误描述的异常
     *
     * @param msg 异常消息，描述具体的错误原因
     */
    public ActorInstantiationException(String msg) {
        super(msg);
    }
}
