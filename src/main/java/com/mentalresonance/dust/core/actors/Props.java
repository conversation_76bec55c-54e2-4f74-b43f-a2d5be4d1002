/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import lombok.Getter;

import java.io.Serializable;

/**
 * Props配置类：封装Actor创建状态的配置对象
 *
 * Props是Actor创建的配置载体，包含了创建Actor实例所需的所有信息。
 * 目前主要包含Actor类和构造参数，未来可能会处理更复杂的配置需求。
 *
 * 设计目的：
 * 1. 封装Actor创建参数：将类信息和构造参数打包
 * 2. 支持远程Actor创建：Props可以序列化传输
 * 3. 提供类型安全：确保创建的是Actor的子类
 * 4. 简化API：提供便利的静态工厂方法
 *
 * 使用方式：
 * Props.create(MyActor.class, param1, param2)
 *
 * <AUTHOR>
 */
public class Props implements Serializable {
    /**
     * Actor类：将要创建的Actor实例的类型
     * 必须是Actor的子类，确保类型安全
     */
    @Getter
    final Class<? extends Actor> actorClass;

    /**
     * 构造参数：传递给Actor构造函数的参数数组
     * 支持可变参数，便于传递不同数量的参数
     */
    final Object[] actorArgs;

    /**
     * 私有构造函数：创建Props实例
     *
     * @param clz 要创建的Actor类
     * @param args 传递给Actor构造函数的参数
     */
    Props(Class<? extends Actor> clz, Object... args) {
        actorClass = clz;
        actorArgs = args;
    }

    /**
     * 静态工厂方法：创建Props配置对象
     *
     * 这是创建Props的推荐方式，提供了简洁的API。
     *
     * @param clz 要创建的Actor类
     * @param args 传递给Actor构造函数的参数
     * @return Props配置对象
     */
    public static Props create(Class<? extends Actor> clz, Object... args) {
        return new Props(clz, args);
    }
}
