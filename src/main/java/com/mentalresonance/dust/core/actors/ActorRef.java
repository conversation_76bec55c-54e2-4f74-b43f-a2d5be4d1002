/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import com.mentalresonance.dust.core.msgs.DeadLetter;
import com.mentalresonance.dust.core.msgs.UnWatchMsg;
import com.mentalresonance.dust.core.msgs.WatchMsg;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.nustaq.net.TCPObjectSocket;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.Serializable;
import java.net.URI;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static com.mentalresonance.dust.core.system.ActorSystemConnectionManager.WrappedTCPObjectSocket;


/**
 * Actor引用：访问Actor实例的代理对象
 *
 * ActorRef是Actor的代理和通信接口，提供了位置透明的消息传递能力。
 * 通常每个Actor只有一个共享的ActorRef实例，这个实例就是Actor本身的'self'变量。
 *
 * 主要功能：
 * 1. 消息发送：通过tell()方法发送消息
 * 2. 位置透明：无论Actor在本地还是远程都使用相同的API
 * 3. 生命周期管理：跟踪Actor的状态和生命周期
 * 4. 监视功能：支持监视其他Actor的生命周期
 * 5. 死信处理：处理无法投递的消息
 *
 * <AUTHOR>
 */
@Slf4j
public class ActorRef implements Serializable {

    /**
     * Actor上下文：提供系统级服务和配置（瞬态字段，不参与序列化）
     */
    final transient ActorContext context;

    /**
     * 关联的Actor实例（瞬态字段，仅本地Actor有效）
     */
    transient Actor actor;

    /**
     * 主机地址：如果进行远程通信，接收方需要通过host + path找到发送方
     */
    String host = null;

    /**
     * 死信标志：如果为true，表示这实际上是一个死信引用（即路径不存在）
     * 此时tell()调用会将消息包装在DeadLetter中
     */
    @Setter
    Boolean isDeadLetter = false;

    /**
     * 异常标志：如果Actor发生异常则设置此字段
     *
     * 即使在异常情况下也会调用postStop()，但这有时是不希望的
     * （例如，持久化Actor可能会在postStop中销毁其状态，假设不再需要）
     */
    public Throwable isException = null;

    /**
     * 运行Actor的线程（本地Actor有效，远程Actor为null）
     */
    public transient Thread thread = null;

    /**
     * 邮箱实例（本地Actor有效，远程Actor为null）
     */
    public transient Actor.MailBox mailBox = null;

    /**
     * Actor路径：从根上下文'/'到当前Actor的完整路径，总是以'/'结尾
     */
    public String path;

    /**
     * 祖先路径数组：从根'/'到当前Actor的所有路径名称
     */
    public String[] ancestors;

    /**
     * Actor名称（本地Actor有效，远程Actor为null）
     */
    public String name = null;

    /**
     * 创建此Actor的Props配置（本地Actor有效，远程Actor为null）
     */
    public transient Props props;

    /**
     * 导致重启的异常信息
     */
    public transient Throwable restartCause = null;

    /**
     * 生命周期状态：标识Actor当前所处的生命周期阶段
     *
     * Actor在启动时检查此状态（判断是启动还是重启），
     * 以及在消息处理循环中发生异常后恢复时检查此状态。
     */
    public transient Integer lifecycle = LC_START;

    /**
     * 生命周期常量：常规启动状态
     */
    public final static int LC_START = 0;

    /**
     * 生命周期常量：重启状态，restartCause将包含错误信息
     */
    public final static int LC_RESTART = 1;

    /**
     * 生命周期常量：在线程恢复时停止
     */
    public final static int LC_STOP = 2;

    /**
     * 生命周期常量：恢复消息处理
     */
    public final static int LC_RESUME = 3;

    /**
     * 生命周期常量：已完成恢复
     */
    public final static int LC_RECOVERED = 4;

    /**
     * 生命周期常量：在中断处理时将转换为LC_RESUME
     */
    public final static int LC_INTERRUPT_RESUME = 5;

    /**
     * 生命周期常量：在中断处理时将转换为LC_RESTART
     */
    public final static int LC_INTERRUPT_RESTART = 6;

    /**
     * 在指定上下文中构造指定路径的ActorRef
     *
     * @param path Actor的路径
     * @param context 使用的上下文
     * @param actor 关联的Actor实例
     */
    public ActorRef(String path, ActorContext context, Actor actor) {
        this.path = path;
        this.context = context;
        this.host = context.hostContext;
        this.actor = actor;
        makeAncestors();
    }

    /**
     * 在指定上下文中构造具有给定名称和父路径的ActorRef
     *
     * @param parentPath 父Actor的路径（创建者的路径）
     * @param name 构造的Actor名称
     * @param context 使用的上下文
     * @param actor 关联的Actor实例
     */
    public ActorRef(String parentPath, String name, ActorContext context, Actor actor) {
        this.path = parentPath + name + "/";
        this.context = context;
        this.name = name;
        this.host = context.hostContext;
        this.actor = actor;
        makeAncestors();
    }

    /**
     * 向此ActorRef对应的Actor发送消息，并标记相应的发送者
     *
     * @param message 要发送的消息
     * @param sender 消息发送者
     * @return 如果发送无错误返回true，否则返回false。不表示成功接收。
     */
    public boolean tell(Serializable message, ActorRef sender) {
        boolean success = true;

        // log.trace("从{}向{}的邮箱投递消息：{}", sender, this, message);

        try {
            SentMessage sentMessage;

            /*
             * 如果是死信，我知道有一个活跃的邮箱（DeadLetterActor邮箱），
             * 所以只需将消息包装在DeadLetter消息中并发送
             */
            if (isDeadLetter) {
                log.trace("{}是死信邮箱", this);
                message = new DeadLetter(message, path, sender);
            }

            sentMessage = new SentMessage(message, sender);

            if (mailBox != null) { // 本地Actor
                if (! mailBox.dead) {
                    // log.trace("添加消息：{}到邮箱：{}，队列预大小={}", message, this, mailBox.queue.size());
                    mailBox.queue.add(sentMessage);
                }
                else {
                    log.trace("{}邮箱已死亡...是否重启了？", this);
                    if (!PersistentActor.isInShutdown()) { // 可能正在关闭但为false -- 需要修复这个问题
                        ActorRef deadLetterRef = context.getDeadLetterActor();
                        if (deadLetterRef != null && !deadLetterRef.mailBox.dead) { // 我们可能正在全局停止
                            deadLetterRef.tell(new DeadLetter(sentMessage.message, path, sender), null);
                        }
                    }
                }
            }
            else {
                // 远程Actor消息发送
                if (! path.contains(":"))
                    path = host + path;
                try {
                    sentMessage.remotePath = path;
                    sendMessage(sentMessage, path);
                }
                catch (InterruptedException ie) {
                    log.error("无法获取到{}的套接字：被中断", path);
                    success = false;
                }
                catch (Exception e) {
                    log.error("无法获取到{}的套接字：{}", path, e.getMessage());
                    e.printStackTrace();
                    success = false;
                }
            }
        }
        catch (Throwable e) {
            log.error("发送消息时发生异常：{}", e.getMessage());
            e.printStackTrace();
            success = false;
        }
        return success;
    }

    /**
     * 尝试发送消息：如果失败则抛出异常
     *
     * 实现了重试机制，最多重试10次，每次失败后等待5秒。
     * 使用连接池管理TCP连接，提高网络通信效率。
     *
     * @param sentMessage 要发送的消息
     * @param path 目标路径
     * @throws Exception 如果所有重试都失败
     */
    private void sendMessage(SentMessage sentMessage, String path) throws Exception {
        WrappedTCPObjectSocket wrappedTCPObjectSocket = null;
        TCPObjectSocket socket;
        URI uri = new URI(path);
        Exception lastException = null;

        // 重试机制：最多重试10次
        for (int i = 0; i < 10; i++) {
            try {
                // 从连接池获取套接字
                wrappedTCPObjectSocket = context.system.getSocket(uri);
                socket = wrappedTCPObjectSocket.tcpObjectSocket;

                // 发送消息对象
                socket.writeObject(sentMessage);
                socket.flush();

                // 等待服务器的应用级"ACK"确认
                BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getSocket().getInputStream()));
                reader.readLine();
                return; // 成功发送，直接返回
            }
            catch (Exception e) {
                lastException = e;
                // 清空连接池中的失效连接
                context.system.flushPool(uri);
                // 等待5秒后重试
                Thread.sleep(5000L);
            }
            finally {
                // 归还套接字到连接池
                if (null != wrappedTCPObjectSocket)
                    context.system.returnSocket(wrappedTCPObjectSocket);
            }
        }
        // 所有重试都失败，抛出最后一个异常
        throw lastException;
    }

    /**
     * 将Actor的绝对路径（反向）转换为包含相应ActorRef的CompletableFuture
     *
     * 这个方法用于异步解析Actor路径，支持复杂的路径查找操作。
     * 路径解析是递归进行的，从当前Actor开始向下查找。
     *
     * @param path 反向路径段列表
     * @return 包含相应ActorRef的CompletableFuture
     */
    protected CompletableFuture<ActorRef> resolve(List<String> path) {
        CompletableFuture<ActorRef> ref = new CompletableFuture<>();
        resolve(path, ref);
        return ref;
    }

    /**
     * 递归解析路径的内部方法
     *
     * @param path 剩余的路径段列表
     * @param ref 用于返回结果的CompletableFuture
     */
    private void resolve(List<String> path, CompletableFuture<ActorRef> ref) {
        // log.trace("引用{}收到解析消息：{}", this.path, path);
        if (path.isEmpty()) {
            // 路径解析完成，返回当前Actor
            if (!ref.complete(this)) {
                log.error("解析路径{}未能正确完成", path);
            }
        }
        else {
            // 继续解析下一个路径段
            String next = path.removeFirst();
            try {
                ActorRef child = actor.children.get(next);

                if (null == child) {
                    // log.trace("解析中：{}不是{}的子Actor", next, path);
                    ref.complete(null); // 找不到对应的子Actor
                } else {
                    // log.trace("转发解析请求到{}，它是{}的子Actor", child, path);
                    child.resolve(path, ref); // 递归解析剩余路径
                }
            } catch(Exception e) {
                log.error("路径解析错误：{}", e.getMessage());
            }
        }
    }

    /**
     * 确保此ActorRef的路径包含主机信息
     *
     * 用于远程通信时，确保路径包含完整的主机和端口信息。
     *
     * @return 可能修改了路径的自身引用
     */
    ActorRef remotify() {
        if (! path.contains(":"))
            path = host + path;
        return this;
    }

    /**
     * 将当前Actor添加到监视者列表中，当被监视者停止时会收到Terminated消息
     *
     * @param watchee 被监视的Actor
     */
    public void watch(ActorRef watchee) {
        watchee.tell(new WatchMsg(), this);
    }

    /**
     * 将当前Actor从监视者列表中移除，不再接收被监视者的Terminated消息
     *
     * @param watchee 被监视的Actor
     */
    public void unwatch(ActorRef watchee) {
        watchee.tell(new UnWatchMsg(), this);
    }

    /**
     * 检查此引用是否指向死信Actor
     *
     * @return 如果是死信Actor则返回true
     */
    public boolean isDeadLetter() { return isDeadLetter; }

    /**
     * 阻塞调用线程直到此Actor停止
     *
     * 注意：线程中断是Actor关闭的自然操作的一部分，所以中断不应该传播到这里。
     * 如果传播了，那就是一个错误。
     *
     * 对远程Actor调用waitForDeath也是错误的（目前不支持）。
     *
     * @throws Exception 如果Actor是远程的，目前不支持此操作
     */
    public void waitForDeath() throws Exception {
        try {
            if (null == thread) {
                throw new Exception("{}是远程Actor。无法对远程Actor执行waitForDeath操作".formatted(path));
            }
            thread.join(); // 等待Actor线程结束
        }
        catch (InterruptedException e) {
            log.error("{}的waitForDeath操作被中断！", path);
        }
    }

    /**
     * 字符串表示：返回Actor的路径
     *
     * @return Actor路径字符串
     */
    @Override
    public String toString() {
        return path;
    }

    /**
     * 获取父Actor的名称
     *
     * @return 父Actor的名称
     */
    public String parentName() {
        return ancestors[ancestors.length - 2];
    }

    /**
     * 获取祖父Actor的名称
     *
     * @return 祖父Actor的名称
     */
    public String grandParentName() {
        return ancestors[ancestors.length - 3];
    }

    /**
     * 获取曾祖父Actor的名称
     *
     * @return 曾祖父Actor的名称
     */
    public String greatGrandParentName() {
        return ancestors[ancestors.length - 4];
    }

    /**
     * 将暂存的消息放到邮箱的*前面*
     *
     * 这个方法用于实现消息的暂存和恢复功能。暂存的消息会被优先处理。
     * 使用双锁机制确保操作的原子性。
     *
     * @param stash 暂存的消息列表
     */
    public void unstashAll(List<SentMessage> stash) {
        Object[] newMsgs;

        // 获取双锁以确保操作的原子性
        mailBox.queue.takeLock.lock();
        mailBox.queue.putLock.lock();

        try {
            // 保存当前邮箱中的所有消息
            newMsgs = mailBox.queue.toArray();
            mailBox.queue.clear();

            // 首先添加暂存的消息（优先处理）
            mailBox.queue.addAll(stash);

            // 然后添加原有的消息
            for (Object o : newMsgs) {
                mailBox.queue.add((SentMessage) o);
            }
        } finally {
            // 释放锁
            mailBox.queue.takeLock.unlock();
            mailBox.queue.putLock.unlock();
        }
    }

    /**
     * 构建祖先路径数组的私有方法
     *
     * 将Actor的完整路径分解为祖先路径数组，便于路径导航和查找。
     * 处理根路径"/"和以"/"结尾的路径的特殊情况。
     */
    private void makeAncestors() {
        String newpath = path;

        // 根路径特殊处理
        if ("/".equals(path)) {
            ancestors = new String[0];
            return;
        }
        // 移除末尾的"/"
        else if (path.endsWith("/"))
            newpath = path.substring(0, path.length()-1);

        // 按"/"分割路径
        ancestors = newpath.split("/");
    }
}
