/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.msgs.ReturnableMsg;

import java.util.Objects;

/**
 * 一个Actor，它用自己的地址替换Returnable中的返回地址，将消息发送给原始目标。
 * 然后在收到返回消息时，将原始返回地址放回，将消息作为来自目标的消息发送给父级，
 * 然后停止
 */
public class ReturnableProxyActor extends Actor {

    ActorRef returnRef;
    final ActorRef targetRef;
    final ReturnableMsg msg;

    /**
     * Props
     * @param msg 要代理的消息
     * @param targetRef 消息的目标
     * @return Props
     */
    public static Props props(ReturnableMsg msg, ActorRef targetRef) {
        return Props.create(ReturnableProxyActor.class, msg, targetRef);
    }

    /**
     * 构造函数
     * @param msg 要代理的消息
     * @param targetRef 消息的目标
     */
    public ReturnableProxyActor(ReturnableMsg msg, ActorRef targetRef) {
        this.msg = msg;
        this.targetRef = targetRef;
    }

    @Override
    protected void preStart() {
        returnRef = msg.getSender();
        msg.setSender(self);
        targetRef.tell(msg, parent);
    }

    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            if (Objects.requireNonNull(message) instanceof ReturnableMsg msg) {
                msg.setSender(returnRef);
                parent.tell(msg, sender);
            } else {
                super.createBehavior().onMessage(message);
            }
            stopSelf();
        };
    }
}