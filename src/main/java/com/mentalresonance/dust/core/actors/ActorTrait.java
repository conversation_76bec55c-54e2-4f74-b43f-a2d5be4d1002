/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import java.io.Serializable;

/**
 * 任何设置为ActorTraits的接口都应该扩展此接口，以获得对底层'self'、parent
 * 以及混入Actor的其他有用字段和方法的访问权限
 *
 * <AUTHOR>
 */
public interface ActorTrait {

    /**
     * 由Actor中的self实现
     * @return myself
     */
    ActorRef getSelf();

    /**
     * 由Actor中的parent实现
     * @return my parent
     */
    ActorRef getParent();

    /**
     * 监视Actor
     * @param ref 要监视的Actor
     * @return 被监视的Actor
     */
    ActorRef watch(ActorRef ref);

    /**
     * 取消监视Actor
     * @param ref 要取消监视的actor
     */
    void unWatch(ActorRef ref);

    /**
     * 由Actor中的context实现
     * @return ActorContext
     */
    ActorContext getContext();

    /**
     * 变为指定的行为
     * @param behavior 行为
     */
    void become(ActorBehavior behavior);

    /**
     * 暂存当前行为然后变为新的行为
     * @param newBehavior 要变为的行为
     */
    void stashBecome(ActorBehavior newBehavior);
    /**
     * 变为行为栈顶部的行为并弹出它
     * @return 下一个行为
     * @throws Exception 发生错误时
     */
    ActorBehavior unBecome() throws Exception;

    /**
     * 向自己发送消息
     * @param msg 要发送的消息
     * @return 如果发送消息没有问题则返回true，否则返回false。注意这不说明接收情况。
     */
    boolean tellSelf(Serializable msg);

    /**
     * 获取最后一条消息的发送者
     * @return sender
     */
    ActorRef getSender();
}