/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import com.mentalresonance.dust.core.msgs.*;
import com.mentalresonance.dust.core.services.PersistenceService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Objects;

/**
 * 持久化Actor基类：希望保存重要状态并在Actor重启时恢复的Actor的基类
 *
 * PersistentActor扩展了普通Actor的功能，提供了状态持久化能力。
 * 使用外部数据库（通过{@link PersistenceService}管理）来保存和恢复状态。
 *
 * 主要特性：
 * 1. 状态快照：支持将Actor状态保存为快照
 * 2. 自动恢复：Actor重启时自动恢复之前保存的状态
 * 3. 扩展生命周期：提供额外的生命周期钩子方法
 * 4. 灵活持久化：支持多种持久化实现（文件系统、数据库等）
 * 5. 错误处理：提供持久化操作的成功/失败回调
 *
 * 生命周期：
 * 1. 创建 -> 2. 恢复状态 -> 3. postRecovery() -> 4. preStart() -> 5. 正常运行
 *
 * <p>PersistentActor具有扩展的生命周期管理。</p>
 *
 * <AUTHOR>
 */
@Slf4j
public class PersistentActor extends Actor {
    /**
     * 关闭标志：当接收到（非暴力）终止信号时设置
     *
     * 这使得PersistentActor能够判断它们是被干净地停止（通常意味着丢弃状态）
     * 还是应该保留状态（可能需要在停止前进一步持久化）。
     */
    @Getter
    @Setter
    private static boolean inShutdown = false;

    /**
     * 使用中的持久化服务：负责状态的保存和加载
     */
    protected PersistenceService persistenceService = null;

    /**
     * 默认构造函数：创建持久化Actor实例
     */
    public PersistentActor() {}

    /**
     * 获取唯一标识符：可以被重写
     *
     * 此标识符用作持久化状态的主数据库键。
     * 默认实现返回Actor的路径，确保每个Actor有唯一的持久化标识。
     *
     * @return 唯一标识符
     */
    protected String persistenceId() {
        return self.path;
    }

    /**
     * 获取持久化服务：定义快照的保存方式
     *
     * 默认使用ActorSystem中定义的持久化服务（通常使用文件系统），
     * 但此方法可以被重写以使用自定义的持久化实现。
     *
     * @return 当前系统的持久化服务
     */
    protected PersistenceService getPersistence()  {
        return context.getSystem().getPersistenceService();
    }

    /**
     * 恢复后回调：在快照消息投递后总是被调用，即使状态为null
     *
     * 注意：无法保证何时调用此方法，所以Actor可能仍处于恢复行为中。
     * 在postRecovery()调用后，preStart()将照常被调用。
     * 默认的postRecovery()什么都不做，子类可以重写以执行恢复后的初始化。
     */
    protected void postRecovery() {
        // log.info("{}：调用默认postRecovery方法", self.path);
    }

    /**
     * 保存快照：将对象保存到persistenceId下
     *
     * 异步保存状态，完成后向自己发送SnapshotSuccessMsg或SnapshotFailureMsg。
     * 这种异步设计确保持久化操作不会阻塞Actor的消息处理。
     *
     * @param object 要保存的状态对象
     */
    protected void saveSnapshot(Serializable object) {
        Serializable msg = null;
        try {
            persistenceService.write(persistenceId(), object);
            msg = new SnapshotSuccessMsg();
            log.debug("{}：快照保存成功", self.path);
        }
        catch (Exception e) {
            msg = new SnapshotFailureMsg(e);
            log.error("{}：快照保存失败：{}", self.path, e.getMessage(), e);
        }
        finally {
            self.tell(msg, self);
        }
    }
    /**
     * Deletes my snapshot. Send myself a DeleteSnapshotSuccessMsg or DeleteSnapshotFailureMsg
     */
    protected void deleteSnapshot() {
        Serializable msg = null;
        try {
            persistenceService.delete(persistenceId());
            msg = new DeleteSnapshotSuccessMsg();
        }
        catch (Exception e) {
            msg = new DeleteSnapshotFailureMsg(e);
        }
        finally {
            self.tell(msg, self);
        }
    }
    /**
     * To be overriden. Return the Class of the state being snapshotted/recovered. If not null
     * then the read snapshot will attempt to force the read to be of this class else it uses
     * its heuristics to determine the class to instantiate
     * @return default null
     */
    protected Class<?> getSnapshotClass() {
        return null;
    }


    /*
     * We need to restore our state then call the (no persistent) Actor run
     */
    @Override
    public void run()
    {
        // Get state and chose the recovery behavior
        try {
            Class<?> clz;

            persistenceService = getPersistence();
            Serializable state = (null == (clz = getSnapshotClass())) ?
                    persistenceService.read(persistenceId()) :
                    persistenceService.read(persistenceId(), clz);

            recoveryBehavior().onMessage(new SnapshotMsg(state));
        }
        catch (Exception e) {
            throw new RuntimeException(e);
        }
        self.lifecycle = ActorRef.LC_RECOVERED;
        super.run();
    }
    /**
     * The behavior which defines state recovery. This is the behavior when a PersistentActor is started and it
     * will be sent a {@link SnapshotMsg}. It is up to the implementor to access the state via snapshotmsg.getSnapshot()
     * which will be null if no previous persisted state exists.
     * <p>
     *     The default recoveryBehavior() throws away the snapshot and warns if any other message is received. Typically
     *     an implementation would stash() unhandled messages at this point.
     * </p>
     * <p>
     *     After restoring state the next step is usually to become(createBehavior) and unstasahall() messages.
     * </p>
     * @return the behavior
     */
    protected ActorBehavior recoveryBehavior() {
        return message -> {
            if (Objects.requireNonNull(message) instanceof SnapshotMsg msg) {
                log.warn("%s did not handle Snapshot Recovery %s".formatted(self.path, msg.getSnapshot()));
                become(createBehavior());
            } else {
                log.error("%s received unexpected message %s in recoveryBehavior".formatted(self.path, message));
            }
        };
    }

    /**
     * This absorbs snapshot related messages.
     * @return - behavior
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch(message)
            {
                case SnapshotSuccessMsg ignored -> {}

                case DeleteSnapshotSuccessMsg ignored -> {}

                case DeleteSnapshotFailureMsg fail ->
                        log.error("%s got unhandled DeleteSnapshotFailureMsg %s".formatted(self.path, fail.getException()));

                case SnapshotFailureMsg fail ->
                        log.error("%s got unhandled SnapshotFailureMsg %s".formatted(self.path, fail.getException()));

                default -> super.createBehavior().onMessage(message);
            }
        };
    }
}
