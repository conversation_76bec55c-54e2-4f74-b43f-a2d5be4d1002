/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.*;
import com.mentalresonance.dust.core.msgs.*;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * 持久化服务管理器Actor：维护一个（相同的）服务Actor的虚拟池
 *
 * 当有空闲槽位时，将传入的消息分发给这些Actor处理。
 * 这是一个负载均衡和任务分发的实现。
 *
 * 重要约束：
 * - 服务Actor<b>必须</b>在处理完一个任务后死亡
 * - 这确保了资源的及时释放和状态的清理
 *
 * 持久化特性：
 * - 这个版本的ServiceManagerActor是持久化的
 * - 定期保存消息队列以防中断
 * - 支持系统重启后的任务恢复
 *
 * 使用场景：
 * 1. 批处理任务的分发
 * 2. 计算密集型任务的并行处理
 * 3. 需要限制并发数的服务调用
 * 4. 需要持久化的任务队列管理
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class PersistentServiceManagerActor extends PersistentActor {

    /**
     * 服务Worker的Props配置：用于创建服务Actor实例
     */
    final Props serviceProps;

    /**
     * 最大Worker数量：限制同时运行的服务Actor数量
     */
    final int maxWorkers;

    /**
     * 当前Worker数量：正在运行的服务Actor数量
     */
    int currentWorkers;

    /**
     * 快照保存间隔：两次快照保存之间的毫秒数
     */
    final Long delayMS;

    /**
     * 脏标志：标识消息队列是否有变化需要保存
     */
    boolean dirty;

    /**
     * 定时器句柄：用于定期触发快照保存
     */
    Cancellable bump;

    /**
     * 消息队列：存储待处理的消息和发送者信息
     * 格式：[消息, 发送者]
     */
    final LinkedList<LinkedList<Serializable>> msgQ = new LinkedList<>();

    /**
     * 创建Props配置（使用默认5秒快照周期）
     *
     * @param serviceProps 服务Worker的Props配置
     * @param maxWorkers 最大Worker数量
     * @return Props配置对象
     */
    public static Props props(Props serviceProps, Integer maxWorkers) {
        return Props.create(PersistentServiceManagerActor.class, serviceProps, maxWorkers, 5000L);
    }

    /**
     * 创建Props配置（自定义快照周期）
     *
     * @param serviceProps 服务Worker的Props配置
     * @param maxWorkers 最大Worker数量
     * @param delayMS 快照保存间隔（毫秒）
     * @return Props配置对象
     */
    public static Props props(Props serviceProps, Integer maxWorkers, Long delayMS) {
        return Props.create(PersistentServiceManagerActor.class, serviceProps, maxWorkers, delayMS);
    }

    /**
     * 构造函数：创建持久化服务管理器Actor
     *
     * @param serviceProps 服务Worker的Props配置
     * @param maxWorkers 最大Worker数量
     * @param delayMS 快照保存间隔（毫秒）
     */
    public PersistentServiceManagerActor(Props serviceProps, Integer maxWorkers, Long delayMS) {
        this.serviceProps = serviceProps;
        this.maxWorkers = maxWorkers;
        this.delayMS = delayMS;
        currentWorkers = 0;
        dirty = false;
    }

    /**
     * 构造函数：使用默认5秒快照间隔
     *
     * @param serviceProps 服务Worker的Props配置
     * @param maxWorkers 最大Worker数量
     */
    public PersistentServiceManagerActor(Props serviceProps, Integer maxWorkers) {
        this(serviceProps, maxWorkers, 5000L);
    }

    /**
     * 启动前初始化：设置定时器并开始处理
     */
    @Override
    protected void preStart() {
        // 启动定期快照保存定时器
        bump = scheduleIn(new NextMsg(), delayMS);
        // 触发初始处理
        self.tell(new StartMsg(), self);
    }

    /**
     * 停止后清理：取消定时器并保存最终状态
     */
    @Override
    protected void postStop() {
        // 取消定时器
        bump.cancel();
        // 如果有未保存的变更，进行最终保存
        if (dirty)
            saveSnapshot(msgQ);
    }

    /**
     * 创建默认行为：处理各种消息类型
     *
     * @return 消息处理行为
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch(message) {
                case StartMsg ignored -> {
                    // 启动消息：如果有待处理消息且有空闲Worker槽位，则创建新Worker
                    if (msgQ.size() != 0 && currentWorkers < maxWorkers) {
                        LinkedList<Serializable> record = msgQ.removeFirst();
                        // 创建新的服务Actor并监视它，然后发送消息
                        watch(actorOf(serviceProps)).tell(record.getFirst(), (ActorRef) record.getLast());
                        ++currentWorkers;
                        dirty = true; // 标记状态已变更
                    }
                }

                case Terminated ignored -> {
                    // Worker终止消息：减少当前Worker数量并尝试启动新的处理
                    --currentWorkers;
                    self.tell(new StartMsg(), self);
                }

                case SnapshotSuccessMsg ignored -> {
                    // 快照保存成功：无需特殊处理
                }

                case DeleteSnapshotSuccessMsg ignored -> {
                    // 快照删除成功：无需特殊处理
                }

                case SnapshotFailureMsg msg -> {
                    // 快照保存失败：记录错误
                    log.error("快照保存失败：{}", msg.getException().getMessage());
                }

                case DeleteSnapshotFailureMsg msg -> {
                    // 快照删除失败：记录错误
                    log.error("快照删除失败：{}", msg.getException().getMessage());
                }

                case NextMsg ignored -> {
                    // 定时快照保存消息
                    if (dirty) {
                        saveSnapshot(msgQ); // 保存消息队列状态
                        dirty = false; // 重置脏标志
                    }
                    // 安排下次快照保存
                    scheduleIn(message, delayMS);
                }

                default -> {
                    // 默认消息处理：业务消息
                    if (currentWorkers < maxWorkers) {
                        // 有空闲Worker槽位，直接创建Worker处理
                        watch(actorOf(serviceProps)).tell(message, sender);
                        ++currentWorkers;
                    }
                    else {
                        // 无空闲槽位，将消息加入队列等待
                        msgQ.add(new LinkedList<>(List.of(message, sender)));
                        dirty = true; // 标记状态已变更
                    }
                }
            }
        };
    }
}
