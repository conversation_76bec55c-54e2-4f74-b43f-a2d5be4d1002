/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import com.mentalresonance.dust.core.msgs.*;
import com.mentalresonance.dust.core.system.exceptions.ActorInstantiationException;
import com.mentalresonance.dust.core.utils.DustLinkedBlockingQueue;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.util.*;
import static com.mentalresonance.dust.core.actors.SupervisionStrategy.*;

/**
 * 所有Dusty Actor的基类。
 *
 * Actor是一个轻量级的计算单元，它通过消息传递与其他Actor进行通信。
 * 每个Actor都有自己的状态和行为，并且在单独的线程中运行，确保线程安全。
 *
 * 主要特性：
 * - 消息驱动：通过接收和处理消息来执行操作
 * - 层次结构：Actor形成父子关系的树状结构
 * - 监督策略：父Actor可以监督子Actor的异常处理
 * - 状态封装：每个Actor维护自己的私有状态
 * - 位置透明：Actor可以在本地或远程运行
 */
@Slf4j
public class Actor implements Runnable {

    /**
     * 默认的监督策略：ONE_FOR_ONE / STOP
     * 当子Actor发生异常时，只停止出错的Actor，不影响其他兄弟Actor
     */
    protected final static SupervisionStrategy defaultStrategyFactory = new SupervisionStrategy();

    /**
     * Actor的上下文环境 {@link ActorContext}
     * 提供Actor运行所需的系统级服务和配置信息
     */
    @Getter
    @Setter
    protected ActorContext context;

    /**
     * 父Actor的引用
     * 在Actor层次结构中，每个Actor都有一个父Actor（除了根Guardian Actor）
     */
    @Getter
    @Setter
    protected ActorRef parent;

    /**
     * 祖父Actor的引用
     * 用于某些特殊的消息传递场景，保持对上级Actor的引用
     */
    @Getter
    @Setter
    protected ActorRef grandParent;

    /**
     * 当前Actor的自引用
     * 用于向自己发送消息或在消息中标识自己
     */
    @Setter
    @Getter
    protected ActorRef self;

    /**
     * 当前正在处理的消息的发送者Actor引用
     * 在处理消息时，可以通过此字段获取消息来源，用于回复消息
     */
    @Getter
    protected ActorRef sender;

    /**
     * 调试标志：如果设置为true，在严重错误时输出调试信息
     * 这是一个全局设置，影响所有Actor的调试行为
     */
    @Setter
    private static boolean debug = false;

    /**
     * 当前活跃的行为处理器
     * 定义了Actor如何响应接收到的消息，可以动态切换行为
     */
    protected ActorBehavior behavior;

    /**
     * 子Actor集合：存储当前Actor创建的所有子Actor
     * key为子Actor的名称，value为子Actor的引用
     */
    protected final HashMap<String, ActorRef> children = new HashMap<>(8);

    /**
     * 监视者列表：记录正在监视当前Actor的其他Actor
     * 当当前Actor终止时，会向所有监视者发送Terminated消息
     */
    private final List<ActorRef> watchers = new LinkedList<>();

    /**
     * 同步锁对象：用于保护Actor内部状态的线程安全访问
     */
    private final Object LOCK = new Object();

    /**
     * 行为栈：支持Actor动态切换行为，使用栈结构管理行为的嵌套
     * 可以通过become()方法切换行为，通过unbecome()恢复之前的行为
     */
    private final ArrayDeque<ActorBehavior> behaviors = new ArrayDeque<>();

    /**
     * 当前活跃的监督策略 {@link SupervisionStrategy}
     * 定义了当子Actor发生异常时的处理方式（重启、停止、恢复等）
     */
    protected SupervisionStrategy supervisor = defaultStrategyFactory;

    /**
     * 暂存消息队列：FIFO结构存储被暂存的消息
     * 当Actor处于特定状态（如恢复中）时，可以暂存消息稍后处理
     */
    protected List<SentMessage> stashed = new LinkedList<>();

    /**
     * 可选的死人开关句柄：用于实现超时机制
     * 可以设置定时器，在指定时间后自动触发某些操作
     */
    private Cancellable deadMansHandle = null;

    /**
     * 运行状态标志：
     * - running: true表示Actor正在处理消息
     * - stopping: true表示Actor正在执行关闭流程
     */
    boolean running, stopping = false;

    /**
     * 构造函数。<b>不应该直接调用此构造函数</b>，而应该通过Props.create()来创建Actor实例。
     *
     * 构造函数会根据Actor类型初始化行为：
     * - 对于PersistentActor，使用恢复行为
     * - 对于普通Actor，使用默认创建的行为
     */
    public Actor() {
        behavior = this instanceof PersistentActor ? ((PersistentActor)this).recoveryBehavior() : createBehavior();
    }

    /**
     * 创建默认行为处理器。此方法应该被子类重写以定义Actor的具体行为。
     *
     * 如果Actor没有重写此方法，则所有未处理的（系统）消息都会在这里处理。
     * 为了方便调试，默认实现会记录所有接收到的消息。
     *
     * @return 默认的消息处理行为
     */
    protected ActorBehavior createBehavior() {
        return message -> {
            switch(message) {

                case ChildExceptionMsg msg -> {
                    log.warn("{}：收到子Actor异常消息：{} - {}", self.path, sender, msg.getException().getMessage());
                }
                case CreatedChildMsg msg -> {
                    // 通常被忽略的消息
                    log.trace("{}：无行为处理。无法处理来自{}的消息：{}", self.path, sender, msg);
                }
                default -> {
                    log.warn("{}：无行为处理。无法处理来自{}的消息：{}", self.path, sender, message);
                }
            }
        };
    }

    /**
     * 在Actor干净启动时调用。此时邮箱已设置完成但消息处理尚未开始运行。
     * 子类可以重写此方法来执行初始化逻辑。
     *
     * @throws Exception 在preStart过程中可能抛出异常
     */
    protected void preStart() throws Exception {
        // log.trace("已启动：" + self.path + " 上下文：" + context);
    }

    /**
     * 在Actor停止时调用。此时邮箱不再处理消息，但仍可以发送消息等。
     * 子类可以重写此方法来执行清理逻辑。
     *
     * @throws Exception 在postStop过程中可能抛出异常
     */
    protected void postStop() throws Exception {
        // log.trace("已停止：" + self.path);
    }

    /**
     * 当Actor因错误被其父Actor重启时调用。子类可以重写此方法来处理重启逻辑。
     *
     * @param t 导致重启的错误异常
     */
    protected void preRestart(Throwable t) {
        // log.trace("已重启：" + self.path + " 原因：" + t.getMessage());
    }

    /**
     * 当Actor在停止后被其父Actor恢复时调用。子类可以重写此方法来处理恢复逻辑。
     */
    protected void onResume() {
        // log.trace("已恢复：" + self.path);
    }
    /**
     * 设置死人开关句柄（超时自毁机制）。如果定时器在被取消之前到期，
     * 将调用dying()方法然后停止自己。
     *
     * 这是一种保护机制，防止Actor在异常情况下无限期运行。
     *
     * @param millis 句柄失效前的毫秒延迟时间
     */
    protected void dieIn(long millis) {
        cancelDeadMansHandle();
        deadMansHandle = new Cancellable(Thread.startVirtualThread(
            () -> {
                try {
                    Thread.sleep(millis);
                    dying();
                    stopSelf();
                }
                catch (InterruptedException e) {
                    // 被取消了，正常情况
                }
                catch (Throwable t) {
                    log.warn("死人开关异常：{}", t.getMessage());
                    stopSelf();
                }
            }
        ));
    }

    /**
     * 在死人开关关闭我们之前调用。如果你想自己处理这个事件，可以重写此方法。
     */
    protected void dying() {
        log.warn("{}：通过死人开关触发dying()方法", self.path);
    }

    /**
     * 取消死人开关句柄
     */
    protected void cancelDeadMansHandle() {
        if (null != deadMansHandle) {
            deadMansHandle.cancel();
            deadMansHandle = null;
        }
    }

    /**
     * 让邮箱线程安全休眠。虚拟线程会休眠但会从其宿主线程分离，
     * 所以这*不是*一个昂贵的操作。
     *
     * @param millis 休眠的毫秒数
     */
    protected void safeSleep(Long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            // 有人试图停止我们。所以再次中断我们以实际开始停止过程
            self.thread.interrupt();
        }
    }

    /**
     * 获取当前Actor的所有子Actor
     *
     * @return 子Actor集合
     */
    protected Collection<ActorRef> getChildren() {
        return children.values();
    }

    /**
     * 获取邮箱中等待处理的消息数量
     *
     * 这对于监控Actor的负载和性能调优很有用。
     *
     * @return 当前邮箱的大小
     */
    protected int mailboxSize() {
        return self.mailBox.queue.size();
    }

    /**
     * 监视指定的Actor。如果被监视的Actor停止，监视者将收到Terminated消息。
     *
     * 注意：监视者无法知道Actor是否会被其父Actor重启，只能知道它被停止了，
     * 所以这个机制不应该用于生命周期管理。
     *
     * 注：此方法为public是因为它在ActorTrait中定义
     *
     * @param ref 要监视的Actor
     * @return 返回被监视的Actor引用，支持链式调用
     */
    public ActorRef watch(ActorRef ref) {
        ref.tell(new WatchMsg(), self);
        return ref;
    }

    /**
     * 取消监视指定的Actor（如果之前正在监视的话）
     *
     * @param ref 要取消监视的Actor
     */
    public void unWatch(ActorRef ref) {
        ref.tell(new UnWatchMsg(), self);
    }

    /**
     * 通过将此Actor分配给轻量级线程来（重新）启动邮箱处理。
     *
     * 这是Actor的核心运行方法，负责：
     * 1. 根据生命周期状态初始化Actor
     * 2. 进入消息处理主循环
     * 3. 处理各种异常和中断情况
     */
    public void run()
    {
        try {
            // 根据Actor的生命周期状态执行相应的初始化操作
            switch (self.lifecycle) {
                case ActorRef.LC_START -> {
                    preStart();  // 执行启动前的初始化
                    running = true;
                }
                case ActorRef.LC_RECOVERED -> {
                    // 持久化Actor恢复后的处理
                    ((PersistentActor)this).postRecovery();
                    preStart();
                    running = true;
                }
                /*
                 * 这种情况虽然意外但是可能发生 - actorOf创建Actor并将虚拟线程
                 * 分配给邮箱，但与此同时我们已经停止了新创建的Actor
                 */
                case ActorRef.LC_STOP -> {
                    running = false;
                }
                case ActorRef.LC_RESTART -> {
                    self.isException = null; // 清除异常标志
                    preRestart(self.restartCause);  // 执行重启前的处理
                    running = true;
                }
                case ActorRef.LC_RESUME -> {
                    self.isException = null;  // 清除异常标志
                    onResume();  // 执行恢复处理
                    running = true;
                }
                default -> {
                    log.error("{}：启动时遇到未知的生命周期状态：{}。正在停止。", self.path, self.lifecycle);
                    running = false;
                }
            }
        }
        /*
         * 重启失败（preStart或preRestart抛出异常），所以停止并告知父Actor
         */
        catch (Exception e) {
            if (parent != null)
                parent.tell(new _Throwable(e), self);
            running = false;
        }

        /*
         * 主循环：获取下一条消息，将其交给行为处理器并管理后续处理。
         * 我们首先检查消息是否是内部管理消息（_Convention）或毒丸消息，
         * 否则将其发送给当前定义的用户行为处理器。
         */

        SentMessage sentMessage = null;

        // Actor的消息处理主循环
        while (running)
        {
            try {
                // 从邮箱队列中取出下一条消息（阻塞操作）
                sentMessage = self.mailBox.queue.take();
                sender = sentMessage.sender;  // 设置当前消息的发送者
                log.trace("{}：收到来自{}的消息：{}", self.path, sender, sentMessage.message);
            }
            /*
             * 如果在下面等待LOCK之外被中断，那么一定是有人想要我停止。
             * 这是一个硬停止。所以我开始关闭但保持消息循环运行，
             * 因为这是我确定所有子Actor都已死亡的方式。
             */
            catch (InterruptedException x) {
                log.trace("{}：在处理消息{}时被中断", self.path, sentMessage);
                if (self.lifecycle == ActorRef.LC_INTERRUPT_RESTART) {
                    self.lifecycle = ActorRef.LC_RESTART;
                    running = false;
                } else if (self.lifecycle == ActorRef.LC_INTERRUPT_RESUME) {
                    self.lifecycle = ActorRef.LC_RESUME;
                    running = false;
                } else {
                    self.lifecycle = ActorRef.LC_STOP;
                    startStopping();  // 开始停止流程
                }
                continue;
            }

            /*
             * 如果我持有一条消息，则处理它。如果没有消息但到达这里，
             * 我可能是通过context.stop()被中断的
             */
            try {
                Serializable sentMsg = sentMessage.message;

                if (null == sentMsg) {
                    log.warn("%s sent null message to %s. Ignored".formatted(sender.toString(), self.path));
                }
                else {
                    if (stopping && ! (sentMsg instanceof _Stopped)) {
                        log.trace("{} ignoring sent message {} as I am stopping", self.path, sentMessage);
                        continue;
                    }

                    switch (sentMsg)
                    {
                        /*
                            子Actor已停止。如果我正在停止且这是我的最后一个子Actor，那么我现在可以停止了。
                         */
                        case _Stopped ignored -> {
                            if (null == children.remove(sender.name))
                                log.warn("{}：子Actor：{}不在子Actor列表中但正在停止...", self.path, sender.name);
                            if (stopping && children.isEmpty())
                                running = false;
                        }

                        case WatchMsg ignored -> watchers.add(sender);

                        case UnWatchMsg ignored ->  watchers.remove(sender);

                        case PoisonPill ignored -> {
                            // 无论监督策略如何，我们总是停止
                            self.lifecycle = ActorRef.LC_STOP;
                            startStopping();
                        }

                        case DeleteChildMsg msg -> context.stop(actorSelection("./" + msg.getName()).getRef());

                        /*
                         * 支持ActorContext中的actorOf方法
                         */
                        case ActorContext._CreateChildMsg msg -> msg.ref.complete(actorOf(msg.props, msg.name));

                        // 如果这是一个请求则执行它 - 如果是对我向其他Actor请求的响应则传递给我的行为处理器
                        case GetChildrenMsg msg -> {
                            if (msg.getIsResponse()) {
                                behavior.onMessage(msg);
                            }
                            else {
                                msg.setIsResponse(true);
                                msg.setChildren(children.values().stream().toList());
                                sender.tell(msg, self);
                            }
                        }

                        /*
                         * 子Actor抛出了异常
                         */
                        case _Throwable msg -> {

                            SupervisionStrategy supervisionStrategy = supervisor.strategy(sender, msg.thrown);
                            Collection<ActorRef> flock;

                            log.warn("{}：从{}收到错误：{}", self, sender, msg.thrown.getMessage());
                            log.info(supervisor.toString());

                            if (supervisionStrategy.getMode() == MODE_ALL_FOR_ONE)
                                flock = children.values();  // 一对全部模式：影响所有子Actor
                            else {
                                LinkedList<ActorRef> list = new LinkedList<ActorRef>();
                                list.add(sender);
                                flock = list;  // 一对一模式：只影响出错的子Actor
                            }

                            for(ActorRef child : flock) {
                                child.restartCause = msg.thrown;
                                child.lifecycle = supervisionStrategy.strategy(child, msg.thrown).getStrategy();

                                if (child.lifecycle == ActorRef.LC_RESUME)
                                    child.lifecycle = ActorRef.LC_INTERRUPT_RESUME;
                                else if (child.lifecycle == ActorRef.LC_RESTART)
                                    child.lifecycle = ActorRef.LC_INTERRUPT_RESTART;

                                /*
                                 * 如果子Actor是原始异常抛出者，它将在锁上等待
                                 * 如果不是，我们仍然可以在设置其生命周期状态后中断它
                                 */
                                child.thread.interrupt();
                                /*
                                 * 重启很复杂 - 我们希望发送者引用仍然有效，因为它被许多
                                 * 其他Actor持有，所以我们需要"调整"它以匹配新条件。
                                 *
                                 * 注意：这里存在竞态条件，我的子Actor可能已经通过上面的中断
                                 * 脱离了LOCK并将其生命周期设置为LC_RESTART，所以检查两种情况
                                 */
                                if (child.lifecycle == ActorRef.LC_INTERRUPT_RESTART || child.lifecycle == ActorRef.LC_RESTART) {
                                    child.thread.join(); // 等待子Actor停止
                                    children.put(child.name,  restart(child)); // 重启它并使其成为我的子Actor
                                }
                            }
                            tellSelf(new ChildExceptionMsg(sender, msg.thrown));
                        }

                        case _ChildProxyMsg msg -> children.values().forEach(child -> child.tell(msg.message, sender));

                        default -> {
                            if (null != behavior) {
                                behavior.onMessage(sentMessage.message);
                            }
                            else {
                                log.warn("{}：无法处理来自{}的消息：{}", self.path, sender, sentMessage.message);
                            }
                        }
                    }
                }
            }
            /*
             * 出现了问题。告诉我的父Actor然后在锁上等待。我的父Actor将做出
             * 恢复策略决定，适当地设置我的生命周期，然后中断我的线程。
             */
            catch (Throwable t)
            {
                self.isException = t;

                try { // 有时对消息执行toString()可能会抛出异常！！
                    log.error("{}：处理消息时发生异常：{} - {}", self.path, sentMessage.message, t.getMessage());
                    if (debug) t.printStackTrace();
                }
                catch (Exception e) {
                    log.error("{}：处理不可显示消息时发生异常：{}", self.path, t.getMessage());
                    if (debug) t.printStackTrace();
                }
                if (null != parent) {
                    parent.tell(new _Throwable(t), self);
                    try {
                        synchronized (LOCK) {
                            LOCK.wait();
                        }
                    }
                    /*
                     * 我的父Actor将在self.lifecycle字段中设置我需要做什么
                     */
                    catch (InterruptedException e)
                    {
                        if (self.lifecycle == ActorRef.LC_INTERRUPT_RESTART) {
                            self.lifecycle = ActorRef.LC_RESTART;
                        } else if (self.lifecycle == ActorRef.LC_INTERRUPT_RESUME) {
                            self.lifecycle = ActorRef.LC_RESUME;
                        }
                        log.info("{}的生命周期是：{}", self.path, self.lifecycle);

                        switch (self.lifecycle)
                        {
                            case ActorRef.LC_STOP -> startStopping();

                            case ActorRef.LC_RESTART -> startStopping(); // 停止并让我的父Actor重启我

                            case ActorRef.LC_RESUME -> {
                                try {
                                    onResume();
                                } catch (Exception x) {
                                    log.error("{}的onResume()异常：{}", self, x.getMessage());
                                    // Todo: 该怎么办？？？
                                }
                            }
                            default ->
                                log.error("{}：恢复时遇到未知的生命周期状态：{}", self, self.lifecycle);
                        }
                    }
                }
            }
        } // End of running

        /*
         * 如果我因为一对全部恢复而停止且我不是违规者
         * 那么简单地再次运行
         */

        if (self.lifecycle == ActorRef.LC_RESUME) {
            self.thread = Thread.startVirtualThread(this);
            return;
        }
        /*
         * 我们已经停止 - 清除上下文中的任何缓存引用
         */
        context.decache(self.path);
        /*
         * 如果我因为重启而退出，那么不要告诉父Actor，保持邮箱且不调用postStop()
         */
        if (self.lifecycle != ActorRef.LC_RESTART)
        {
            watchers.forEach((w) -> w.tell(new Terminated(self.name), self));

            try {
                postStop();
                if (null != deadMansHandle)
                    deadMansHandle.cancel();
            }
            catch (Exception e) {
                log.error("{}的postStop()异常：{}", self.path, e.getMessage());
                if (debug)
                    e.printStackTrace();
                // Todo: 该怎么办？？？
            }
            self.mailBox.queue = null;
            self.mailBox.dead = true;
            if (null != parent) {
                parent.tell(new _Stopped(), self);
            }
        }
        cancelDeadMansHandle();
        // log.trace("{} 已停止", self.path);
    }

    /**
     * 如有必要，杀死我们的子Actor并等待直到它们全部停止。
     * 当每个子Actor停止时，我们将收到一个_Stopped消息
     * 如果没有子Actor，则简单地决定我们是否要停止。
     */
    private void startStopping() {
        log.trace("{}正在停止，有{}个子Actor", self.path, children.size());
        ParentException pex = self.isException != null ? new ParentException(self.isException) : null;
        if (! children.isEmpty()) {
            stopping = true;
            children.forEach((name, child) -> {
                child.lifecycle = ActorRef.LC_STOP;
                context.stop(child, pex);
            });
        } else
            running = false;
    }

    /**
     * 用指定的行为替换当前行为
     *
     * @param behavior 新的行为处理器
     */
    public void become(ActorBehavior behavior) {
        this.behavior = behavior;
    }

    /**
     * 暂存当前行为并用新行为替换它
     *
     * 这个方法实现了行为的嵌套切换，允许Actor临时切换到新行为，
     * 稍后可以通过unBecome()恢复到之前的行为。
     *
     * @param newBehavior 要遵循的新行为
     */
    public void stashBecome(ActorBehavior newBehavior) {
        log.info("暂存{}并切换为{}", this.behavior, newBehavior);
        behaviors.push(this.behavior);
        become(newBehavior);
    }

    /**
     * 弹出最后暂存的行为并使其成为当前行为
     *
     * 这个方法与stashBecome()配对使用，实现行为的恢复。
     *
     * @return 暂存的行为
     * @throws Exception 如果暂存栈为空
     */
    public ActorBehavior unBecome() throws Exception {
        ActorBehavior behavior;

        if (! behaviors.isEmpty()) {
            behavior = behaviors.pop();
            // log.trace("弹出行为 = {}", behavior);
            become(behavior);
        } else
            throw new Exception("unBecome时行为队列为空");
        return behavior;
    }

    /**
     * 将所有消息（除了NonDelegatedMsg实例）委托给目标Actor。
     * 在它停止后切换为thenBecome行为并向自己发送thenMsg
     *
     * @param target 被委托的Actor
     * @param thenBecome 目标完成时要切换的行为，如果为null则使用委托时的行为
     * @param thenMsg 要发送给自己的消息（在thenBecome之后），如果为null则不发送
     */
    protected void delegateTo(ActorRef target, ActorBehavior thenBecome, Serializable thenMsg) {
        watch(target);
        stashBecome(delegateBehavior(target, thenBecome, thenMsg));
    }
    /**
     * 目标Actor接管所有（非NonDelegatedMsg）消息处理，直到停止，
     * 之后我们通过stashBecome切换为thenBecome并发送thenMsg来继续。
     *
     * 被委托的Actor也可以向我们发送包装消息的DelegatingMsg。在这种情况下，我们
     * 停止委托Actor，如果包装的消息不为null则发送给自己，
     * 否则发送thenMsg给自己
     *
     * @param target 要委托给的Actor
     * @param thenBecome 如果不为null则stashBecome这个，否则只是unBecome()
     * @param thenMsg 取消委托时发送给自己的消息，如果不为null
     * @return 新的行为处理器
     */
    protected ActorBehavior delegateBehavior(ActorRef target, ActorBehavior thenBecome, Serializable thenMsg) {
        return message -> {
            switch(message) {
                case Terminated ignored -> {
                    if (sender == target) { // 可能从其他地方收到终止消息
                        unBecome();
                        if (null != thenBecome)
                            stashBecome(thenBecome);
                        if (null != thenMsg)
                            tellSelf(thenMsg);
                    }
                }
                case DelegatingMsg dm -> {
                    context.stop(sender);
                    unBecome();
                    if (null != thenBecome)
                        stashBecome(thenBecome);
                    if (null != dm.getMsg())
                        tellSelf(dm.getMsg());
                    else if (null != thenMsg)
                        tellSelf(thenMsg);
                }
                default  ->  {
                    if (message instanceof NonDelegatedMsg)
                        self.tell(message, sender);
                    else
                        target.tell(message, sender);
                }
            }
        };
    }

    /**
     * 将消息添加到暂存队列
     *
     * 暂存机制允许Actor临时保存消息，稍后再处理。
     * 这在Actor需要等待某些条件满足时很有用。
     *
     * @param msg 要暂存的消息
     */
    protected void stash(Serializable msg) {
        stashed.add(new SentMessage(msg, sender));
    }

    /**
     * 将所有暂存的消息按顺序添加到我们的邮箱。这些消息被添加到邮箱的*前面*
     *
     * 这确保了暂存的消息会在新消息之前被处理，保持了消息处理的顺序性。
     */
    protected void unstashAll() {
        self.unstashAll(stashed);
        stashed = new LinkedList<>();
    }

    /**
     * 在约millis毫秒后向自己发送消息
     *
     * @param msg 要发送的消息
     * @param millis 延迟的毫秒数
     * @return 可取消的句柄
     */
    protected Cancellable scheduleIn(Serializable msg, Long millis) {
        return scheduleIn(msg, millis, self);
    }

    /**
     * 在约millis毫秒后向目标Actor发送消息
     *
     * 这个方法使用虚拟线程实现延迟发送，不会阻塞当前线程。
     *
     * @param msg 要发送的消息
     * @param millis 延迟的毫秒数
     * @param target 消息的接收者
     * @return 可取消的句柄
     */
    protected Cancellable scheduleIn(Serializable msg, Long millis, ActorRef target) {
        if (null == target) {
            log.error("调度到null目标");
            return null;
        }
        return new Cancellable(Thread.startVirtualThread(
                () -> {
                    try {
                        Thread.sleep(millis);
                        target.tell(msg, self);
                    }
                    catch (InterruptedException e) { // 取消！！

                    }
                    catch (Throwable t) {
                        log.warn(t.getMessage());
                    }
                }
        ));
    }

    /**
     * 在约millis毫秒后向目标Actor发送消息（指定发送者）
     *
     * @param msg 要发送的消息
     * @param millis 延迟的毫秒数
     * @param target 消息的接收者
     * @param from 作为此Actor发送
     * @return 可取消的句柄
     */
    protected Cancellable scheduleIn(Serializable msg, Long millis, ActorRef target, ActorRef from) {
        if (null == target) {
            log.error("调度到null目标");
            return null;
        }
        return new Cancellable(Thread.startVirtualThread(
                () -> {
                    try {
                        Thread.sleep(millis);
                        target.tell(msg, from);
                    }
                    catch (InterruptedException e) { // 取消！！

                    }
                    catch (Throwable t) {
                        log.warn(t.getMessage());
                    }
                }
        ));
    }

    /**
     * 使用随机名称从props创建子Actor
     *
     * 当此方法返回时，新Actor的邮箱可用并可以发送消息，但不保证
     * preStart()/postRecovery()已被调用。
     *
     * @param props Actor的配置
     * @return ActorRef，失败时返回null
     * @exception ActorInstantiationException 如果无法实例化
     */
    protected ActorRef actorOf(Props props) throws ActorInstantiationException {
        return actorOf(props, UUID.randomUUID().toString());
    }

    /**
     * 使用给定名称从props创建子Actor
     *
     * 当此方法返回时，新Actor的邮箱可用并可以发送消息，但不保证
     * preStart()/postRecovery()已被调用。
     *
     * 注意 - 可能存在竞态条件，即一个Actor正在被创建，另一个也进来
     * 尝试创建它。在这些冲突中，我们记录冲突但返回已存在的ActorRef。
     *
     * @param props Actor的配置
     * @param name Actor的名称
     * @return ActorRef，失败时返回null
     * @exception ActorInstantiationException 如果无法实例化
     */
    protected ActorRef actorOf(Props props, String name) throws ActorInstantiationException {
        try {
            ActorRef exists;
            assert name != null: self.path + "的子Actor给定了null名称！";
            if (null != (exists = children.get(name))) {
                log.warn("{}的子Actor {}已存在！！", self.path, name);
                return exists;
            }
            Actor actor = createInstanceWithParameters(props.actorClass, props.actorArgs);
            ActorRef ref = new ActorRef(self.path, name, context, actor);

            ref.props = props;
            ref.mailBox = new MailBox();

            actor.context = context;
            actor.parent = self;
            actor.grandParent = parent;
            actor.self = ref;
            ref.thread = Thread.startVirtualThread(actor);

            children.put(name, ref);

            return ref;
        }
        catch (Exception e) {
            log.error("无法创建Actor {}，错误：{}", props.actorClass, e.getMessage());
            throw new ActorInstantiationException(e.getMessage());
        }
    }

    /**
     * 使用参数创建Actor实例
     *
     * 这个方法处理基本类型的自动装箱，确保构造函数查找能够正确工作。
     *
     * @param actorClass Actor类
     * @param args 构造函数参数
     * @return Actor实例
     * @throws Exception 如果创建失败
     */
    Actor createInstanceWithParameters(Class<?> actorClass, Object[] args) throws Exception {
        ArrayList<Class<?>> classList = new ArrayList<>();
        classList.add(actorClass);
        for (Object obj : args) {
            if (obj != null) {
                Class clazz = obj.getClass();
                if (clazz.isPrimitive()) {
                    // 处理基本类型的自动装箱
                    if (clazz == int.class) {
                        classList.add(Integer.class);
                    } else if (clazz == boolean.class) {
                        classList.add(Boolean.class);
                    } else if (clazz == byte.class) {
                        classList.add(Byte.class);
                    } else if (clazz == char.class) {
                        classList.add(Character.class);
                    } else if (clazz == double.class) {
                        classList.add(Double.class);
                    } else if (clazz == float.class) {
                        classList.add(Float.class);
                    } else if (clazz == long.class) {
                        classList.add(Long.class);
                    } else if (clazz == short.class) {
                        classList.add(Short.class);
                    } else if (clazz == void.class) {
                        classList.add(Void.class);
                    }
                } else {
                    classList.add(clazz);
                }
            } else {
                classList.add(null);
            }
        }
        Constructor cons = context.actorConstructors.get(classList);
        return (Actor) cons.newInstance(args);
    }

    /**
     * 自动装箱基本类型
     *
     * 将基本类型转换为对应的包装类，用于构造函数查找。
     *
     * @param classList 类列表
     * @return 自动装箱后的类列表
     */
    private  List<Class<?>> autoboxPrimitiveTypes(List<Class<?>> classList) {
        List<Class<?>> autoboxedList = new ArrayList<>();

        for (Class<?> clazz : classList) {
            // 检查类是否为基本类型，如果是，则添加相应的包装类
            if (clazz.isPrimitive()) {
                if (clazz == int.class) {
                    autoboxedList.add(Integer.class);
                } else if (clazz == boolean.class) {
                    autoboxedList.add(Boolean.class);
                } else if (clazz == byte.class) {
                    autoboxedList.add(Byte.class);
                } else if (clazz == char.class) {
                    autoboxedList.add(Character.class);
                } else if (clazz == double.class) {
                    autoboxedList.add(Double.class);
                } else if (clazz == float.class) {
                    autoboxedList.add(Float.class);
                } else if (clazz == long.class) {
                    autoboxedList.add(Long.class);
                } else if (clazz == short.class) {
                    autoboxedList.add(Short.class);
                } else if (clazz == void.class) {
                    autoboxedList.add(Void.class);
                }
            } else {
                // 如果不是基本类型，直接添加类
                autoboxedList.add(clazz);
            }
        }

        return autoboxedList;
    }


    /**
     * 将路径转换为ActorRef。路径可以是以下形式：
     *      /.... 绝对路径
     *      ./... 相对于我的路径
     *      ../... 相对于我父Actor的路径
     *      以上都不是 - 我们认为是相对于我的路径
     * 如果路径包含':'，则预期为远程路径，目前格式为：
     *      dust://host:port/remote-system-name/path
     * 在这种情况下，路径必须是绝对的
     *
     * @param path 所需Actor的路径
     * @return Actor的ActorSelection
     */
    protected ActorSelection actorSelection(String path)
    {
        ActorSelection selection = new ActorSelection();

        path = path.trim();

        if (! path.contains(":"))
        {         // 如果包含:则必须是绝对路径
            if (! path.startsWith("/"))
            {    // 不是绝对路径，可能是相对路径
                if (path.startsWith("../"))
                {
                    // segments的形式为[a, b, c, d, e]
                    // path是../../../foo
                    List<String> segments = new ArrayList<>(Arrays.stream(self.path.split("/")).toList());

                    while (path.startsWith("../")) {
                        segments.remove(segments.size() - 1);
                        path = path.substring(3);
                    }
                    // ../../.. 情况
                    if (path.startsWith("..")) {
                        segments.remove(segments.size() - 1);
                        path = path.substring(2);
                    }

                    path = String.join("/", segments) + "/" + path;
                }
                else if (path.startsWith("./")) {
                    String childPath = path.substring(2);
                    path = self.path + childPath;
                }
                else
                    path = self.path + path;
                /*
                 * 如果我在路径上，那么确保我在引用缓存中，这样解析就不需要经过我
                 */
                if (path.startsWith(self.path)) {
                    context.encache(self.path, self);
                }
            }
        }
        else
            selection.setRemote(true);

        selection.path = path;
        selection.context = context;
        return selection;
    }

    /**
     * 在指定引用处重新启动Actor
     *
     * 我们创建Actor的新实例并修补其上下文和旧的ActorRef。
     * 这个过程保持了ActorRef的有效性，使得其他持有该引用的Actor
     * 仍然可以正常与重启后的Actor通信。
     *
     * @param ref 要重启的Actor引用
     * @return 原始的Actor引用
     * @exception ActorInstantiationException 如果无法实例化
     */
    private ActorRef restart(ActorRef ref) throws ActorInstantiationException {
        try {
            Constructor<?> cons = ref.props.actorClass.getConstructors()[0];
            Actor actor = (Actor)cons.newInstance(ref.props.actorArgs);

            actor.context = context;
            actor.parent = self;
            actor.grandParent = parent;
            actor.self = ref;

            ref.actor = actor;
            ref.lifecycle = ActorRef.LC_RESTART;
            ref.thread = Thread.startVirtualThread(actor);
            // log.trace("{}重启了{}", self.path, ref);
        }
        catch (Exception e) {
            e.printStackTrace();
            throw new ActorInstantiationException(e.getMessage());
        }
        return ref;
    }

    /**
     * 便捷方法：向自己发送消息，等价于self.tell(message, self)
     *
     * @param message 要发送的消息
     * @return 如果发送无错误返回true，否则返回false
     */
    public boolean tellSelf(Serializable message) {
        return self.tell(message, self);
    }

    /**
     * 便捷方法：停止自己，等价于context.stop(self)
     */
    protected void stopSelf() { context.stop(self); }

    /**
     * 邮箱类：包装ActorRef引用的消息队列。
     *
     * 当我们停止一个Actor时，只需将Actor队列的引用替换为死信队列的引用。
     * 这种设计使得Actor的停止操作变得简单高效。
     */
    public static class MailBox {
        /**
         * 死亡标志：标识此邮箱是否已经死亡（不再接收新消息）
         */
        @Getter
        @Setter
        Boolean dead = false;

        /**
         * 消息队列：存储待处理的消息，使用自定义的阻塞队列实现
         */
        @Getter
        DustLinkedBlockingQueue<SentMessage> queue = new DustLinkedBlockingQueue<>();

        /**
         * 创建邮箱实例
         */
        public MailBox() {}
    }

    /*
     * 内部消息类型定义
     */

    /**
     * 内部停止消息：用于标识Actor已停止
     */
    protected class _Stopped implements Serializable { }

    /**
     * 内部异常消息：包装需要传递的异常信息
     */
    static class _Throwable implements Serializable {
        final Throwable thrown;

        public _Throwable(Throwable thrown) {
            this.thrown = thrown;
        }
    }

    /**
     * 子Actor代理消息：包装要发送给所有子Actor的消息
     */
    static class _ChildProxyMsg implements Serializable {
        final Serializable message;
        public  _ChildProxyMsg(Serializable msg) {
            this.message = msg;
        }
    }

    // 开发者辅助工具方法

    /**
     * 调试打印工具：打印带有Actor路径前缀的调试信息
     *
     * @param s 要打印的字符串，会自动添加Actor路径前缀
     */
    protected void println(String s) { System.out.println(self.path + ": " + s); }
}

