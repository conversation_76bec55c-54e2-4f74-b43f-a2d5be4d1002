/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.msgs.PipelineStageMsg;
import com.mentalresonance.dust.core.msgs.ReturnableMsg;
import com.mentalresonance.dust.core.system.exceptions.ActorInstantiationException;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;

/**
 * 管道由PipelineActor管理。其props指定一系列其他Actor Props，它将其实例化为子级。
 * 它维护顺序 -- C1 -> C2 -> C3 ... -- 如果它从CN接收到消息，它只是将该消息传递给CN+1。
 *
 * 现在管道中的Actor必须是唯一的（我们使用它们的名称），除非给出了匹配的不同名称列表。
 *
 * 默认情况下，如果消息不是起源于管道，则它会转到第一个子级。如果消息从管道的最后阶段出现，
 * 并且它是一个ReturnableMsg，则管道将其发送回发送者，否则将其丢弃。
 *
 *  <AUTHOR>
 */
@Slf4j
public class PipelineActor extends Actor {

    /**
     * refN -> refN+1的映射
     */
    final HashMap<ActorRef, ActorRef> pipe = new HashMap<>();

    /**
     * 阶段名称 -> 阶段引用，用于阶段定向消息
     */
    final HashMap<String, ActorRef> stages = new HashMap<>();

    ActorRef first, last = null; // 管道中的Actors

    final List<Props> stageProps;
    final List<String> names;

    /**
     * 构造具有命名Actor的管道
     * @param stageProps 创建管道每个阶段的Props列表
     * @param names 为创建的Actors命名的匹配列表或null
     * @return 管道的Props
     */
    public static Props props(List<Props> stageProps, List<String> names) {
        return Props.create(PipelineActor.class, stageProps, names);
    }
    /**
     * 构造具有随机命名Actor的管道
     * @param stageProps 创建管道每个阶段的Props列表
     * @return 管道的Props
     */
    public static Props props(List<Props> stageProps) {
        return Props.create(PipelineActor.class, stageProps, null);
    }

    /**
     * 构造函数
     * @param stageProps 创建管道每个阶段的Props列表
     * @param names 为创建的Actors命名的匹配列表或null
     */
    public PipelineActor(List<Props> stageProps, List<String> names)  {
        this.stageProps = stageProps;
        this.names = names;
    }

    @Override
    protected void preStart() throws ActorInstantiationException {
        ActorRef ref;

        if (null != names && names.size() != stageProps.size())
            throw new ActorInstantiationException("错误的名称/props大小");

        String name = "";
        try {
            for (int i = 0; i < stageProps.size(); ++i) {
                Props next = stageProps.get(i);

                name = (null == names) ? next.getActorClass().getSimpleName() : names.get(i);
                if (null == last) {
                    first = actorOf(next, name);
                    last = first;
                }
                else {
                    ref = actorOf(next, name);
                    pipe.put(last, ref);
                    last = ref;
                }
            }
            for (ActorRef child : getChildren()) {
                stages.put(child.name, child);
            }
        }
        catch (Exception e) {
            log.error("管道 %s: %s 创建Actor '%s'".formatted(self.path, e.getMessage(), name));
            throw new ActorInstantiationException(e.getMessage());
        }
    }

    /**
     * 默认行为
     * @return {@link ActorBehavior}
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            if (message instanceof PipelineStageMsg msg) {
                ActorRef child;
                if (null != (child = stages.getOrDefault(msg.getStage(), null))) {
                    child.tell(msg.getMsg(), sender);
                }
            }
            else {
                ActorRef to = pipe.get(sender); // 管道中的下一阶段
                if (null == to) {
                    if (sender == last) {
                        if (message instanceof ReturnableMsg)
                            to = ((ReturnableMsg)message).getSender();
                        else
                            log.warn("管道 %s 丢弃消息 %s".formatted(self.path, message));
                    } else
                        to = first;
                }
                if (null != to)
                    to.tell(message, self);
            }
        };
    }
}