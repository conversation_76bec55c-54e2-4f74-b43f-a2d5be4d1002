/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.Props;

/**
 * 空Actor：顾名思义，这个Actor只使用默认的Actor行为，本身不添加任何功能
 *
 * 这是一个简单的Actor实现，使用基础的Actor行为，不执行任何特殊操作。
 * 主要用于系统测试、性能基准测试和作为临时占位符。
 *
 * 使用场景：
 * 1. 单元测试：作为测试中的模拟Actor
 * 2. 性能测试：测试消息传递的基础性能
 * 3. 占位符：在开发过程中作为临时的Actor实现
 * 4. 调试工具：用于跟踪消息流而不影响系统行为
 * 5. 负载测试：创建大量轻量级Actor进行压力测试
 *
 * 特点：
 * - 轻量级：最小的资源占用
 * - 无副作用：不会改变系统状态
 * - 默认行为：使用Actor基类的默认消息处理
 * - 简单可靠：不会抛出异常或产生错误
 *
 * <AUTHOR>
 */
public class NullActor extends Actor {
    /**
     * 创建Actor的Props配置对象
     *
     * @return Actor的Props配置
     */
    public static Props props() {
        return Props.create(NullActor.class);
    }

    /**
     * 默认构造函数：创建NullActor实例
     */
    public NullActor() {}
}
