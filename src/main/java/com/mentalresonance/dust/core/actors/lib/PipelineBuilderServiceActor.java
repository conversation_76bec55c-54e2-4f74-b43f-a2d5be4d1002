package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.msgs.StartMsg;
import com.mentalresonance.dust.core.msgs.StopMsg;
import lombok.Getter;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * 基础Actor，用于构建一对List<Props>, List<Names>并发送使用这对构建的Pipeline props
 * 回给请求者。请求者可以从中构建管道。
 * 客户端发送到这里的消息序列是StartMsg(), StageMsg() .. StageMsg(), StopMsg()
 * 我们不在这里构建管道，因为我们是服务Actor，我们的子级会死亡。
 * 这允许动态创建管道 - 对于代理系统和LLM非常有用...
 */
public class PipelineBuilderServiceActor extends Actor {

    List<Props> props = new LinkedList<>();
    List<String> names = new LinkedList<>();
    ActorRef originalSender;

    public static Props props() { return Props.create(PipelineBuilderServiceActor.class); }

    @Override
    protected ActorBehavior createBehavior() {
        return (Serializable message) -> {
            switch(message) {
                case StartMsg ignored:
                    originalSender = sender;
                    break;

                case StopMsg ignored:
                    originalSender.tell(new PipelineStagesMsg(props, names), self);
                    stopSelf();
                    break;

                case StageMsg msg:
                    props.add(msg.props);
                    names.add(msg.name);
                    break;

                default:
                    throw new IllegalStateException("Unexpected value: " + message);
            }
        };
    }

    @Getter
    public static class PipelineStagesMsg implements Serializable {
        Props pipelineProps;

        public PipelineStagesMsg(List<Props> props, List<String> names) {
            pipelineProps = PipelineActor.props(props, names);
        }
    }

    @Getter
    public static class StageMsg implements Serializable {
        Props props;
        String name;

        public StageMsg(Props props, String name) {
            this.props = props;
            this.name = name;
        }
    }
}