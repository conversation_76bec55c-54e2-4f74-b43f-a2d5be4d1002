/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.Props;
import lombok.extern.slf4j.Slf4j;

/**
 * 日志Actor：顾名思义，记录它接收到的每条消息
 *
 * 这是一个简单的工具Actor，主要用于：
 * 1. 调试和监控消息流
 * 2. 作为消息处理管道中的日志节点
 * 3. 测试和开发阶段的消息跟踪
 * 4. 系统监控和审计
 *
 * LogActor会将所有接收到的消息以INFO级别记录到日志中，
 * 包括消息内容、发送者信息和自身路径。
 *
 * <AUTHOR>
 */
@Slf4j
public class LogActor extends Actor {

    /**
     * 创建Props配置对象
     *
     * @return LogActor的Props配置
     */
    public static Props props() {
        return Props.create(LogActor.class);
    }

    /**
     * 默认构造函数：创建LogActor实例
     */
    public LogActor() {}

    /**
     * 创建默认行为：记录所有接收到的消息
     *
     * @return 消息处理行为，会记录每条消息的详细信息
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            log.info("{}：收到来自{}的消息：{}", self.path, sender, message);
        };
    }

}
