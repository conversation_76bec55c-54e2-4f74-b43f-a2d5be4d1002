/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.*;
import com.mentalresonance.dust.core.msgs.StopMsg;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;

/**
 * 接收Actor列表和用于构造一系列消息的类。
 * 将这些消息的实例发送给给定的Actor，当所有Actor都回复（或超时）后，
 * 将响应映射发送给请求Actor然后死亡。
 *
 *  <AUTHOR>
 */
@Slf4j
public class ReaperActor extends Actor {

    ActorRef client;
    ReapMsg reapMsg;
    Cancellable cancellable;
    Long handleMs;

    /**
     * 构造函数
     * @param handleMs 死人开关触发前的时间（毫秒）
     */
    public ReaperActor(Long handleMs) { this.handleMs = handleMs; }

    /**
     * 创建props
     * @param handleMS 死人开关触发前的时间（毫秒）
     * @return props
     */
    public static Props props(Long handleMS) {
        return Props.create(ReaperActor.class, handleMS);
    }

    @Override
    protected void preStart() {
        dieIn(handleMs);
    }

    @Override
    protected void dying() {
        log.warn("收割者 {} 正在死亡", self.path);
        reapMsg.response.complete = false;
        client.tell(reapMsg.response, null);
    }

    /**
     * 默认行为
     * @return {@link ActorBehavior}
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch(message) {
                case ReapMsg msg -> {
                    client = sender;
                    reapMsg = msg;
                    if (!reapMsg.targets.isEmpty()) {
                        log.trace("{} 正在收割 {} 个目标", self.path, reapMsg.targets.size());
                        if (null != reapMsg.clz)
                            for (ActorRef t : reapMsg.targets) {
                                t.tell(reapMsg.clz.getDeclaredConstructor().newInstance(), self);
                            }
                        else if (null != reapMsg.copyableMsg)
                            for (ActorRef t : reapMsg.targets) {
                                t.tell(reapMsg.copyableMsg.copy(), self);
                            }
                        else
                            self.tell(new StopMsg(), self);
                    }
                    else {
                        self.tell(new StopMsg(), self);
                    }
                }
                case StopMsg ignored -> {
                    client.tell(reapMsg.response, null);
                    cancelDeadMansHandle();
                    stopSelf();
                }
                default -> {
                    reapMsg.response.results.put(sender, message);
                    if (reapMsg.isComplete()) {
                        log.trace("{} 收割完成", self.path);
                        reapMsg.response.complete = true;
                        self.tell(new StopMsg(), self);
                    }
                }
            }
        };
    }

    /**
     * 收割请求。
     */
    public static class ReapMsg implements Serializable
    {
        /**
         * 子级引用 -> 子级消息映射的容器
         */
        public static class ReapResponseMsg implements Serializable {
            /**
             * 响应请求的消息
             * 存储在这里。
             */
            @Getter
            final
            HashMap<ActorRef, Object> results = new HashMap<>();

            /**
             * Reaper是否成功完成所有结果或死人开关触发
             */
            public boolean complete; // 是否收集了所有结果？？

            /**
     * 构造函数
             */
            public ReapResponseMsg() { }
        }

        /**
         * 要发送给发送者的响应。包含子级引用 -> 子级消息的映射
         */
        @Getter
        ReapResponseMsg response;
        /**
         * 将向其发送消息的Actor列表
         * 从ReapActor发送
         */
        @Getter
        final
        List<ActorRef> targets;

        /**
         * 要实例化并发送给目标的消息
         */
        Class<? extends Serializable> clz = null;

        /**
         * clz的替代方案。要复制并发送给目标的消息
         */
        CopyableMsg copyableMsg = null;

        /**
         * 构造函数
         * @param clz 发送此类的新实例
         * @param targets 发送给这些目标
         */
        public ReapMsg(Class<? extends Serializable> clz, List<ActorRef> targets) {
            this.clz = clz;
            this.targets = targets;
            this.response = new ReapResponseMsg();
        }


        /**
         * 使用特定的响应类构造ReapMsg（应该扩展ReapResponseMsg）
         * @param clz 要实例化并发送的消息
         * @param targets 发送给这些Actor
         * @param respClz 自定义ReapResponseMsg类
         * @throws NoSuchMethodException 错误时
         * @throws InstantiationException 错误时
         * @throws IllegalAccessException 错误时
         * @throws InvocationTargetException 错误时
         */
        public ReapMsg(
                Class<? extends Serializable> clz,
                List<ActorRef> targets,
                Class<? extends ReapResponseMsg> respClz) throws NoSuchMethodException, InstantiationException, IllegalAccessException, InvocationTargetException {
            this.clz = clz;
            this.targets = targets;
            this.response = respClz.getDeclaredConstructor().newInstance();
        }

        /**
         * 使用特定的响应类构造ReapMsg（应该扩展ReapResponseMsg）
         * @param copyableMsg 要复制并发送的消息
         * @param targets 发送给这些Actor
         * @param respClz 自定义ReapResponseMsg类
         * @throws NoSuchMethodException 错误时
         * @throws InstantiationException 错误时
         * @throws IllegalAccessException 错误时
         * @throws InvocationTargetException 错误时
         */
        public ReapMsg(
                CopyableMsg copyableMsg,
                List<ActorRef> targets,
                Class<? extends ReapResponseMsg> respClz) throws NoSuchMethodException, InstantiationException, IllegalAccessException, InvocationTargetException {
            this.copyableMsg = copyableMsg;
            this.targets = targets;
            this.response = respClz.getDeclaredConstructor().newInstance();
        }

        /**
         * 我们是否收到了所有响应
         * @return 如果是则返回true
         */
        boolean isComplete() {
            return response.results.size() == targets.size();
        }
    }

    /**
     * 与其创建要传递的消息的新实例，我们可以传入一个可复制的对象
     * 它将被复制而不是新建实例
     */
    public interface CopyableMsg extends Serializable {
        /**
         * 复制消息
         * @return 消息的副本
         */
        Serializable copy();
    }
}