/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib.entities.msgs;


import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * 发送到PodDeadLettersActor以注册对给定消息类型的兴趣
 */
public  class RegisterPodDeadLettersMsg implements Serializable {
    /**
     * 感兴趣的消息
     */
    public final List<Class<?>> acceptedMessages = new LinkedList<>();

    /**
     * 构造函数
     */
    public RegisterPodDeadLettersMsg() {}
}