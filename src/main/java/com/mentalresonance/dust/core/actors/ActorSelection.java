/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import com.mentalresonance.dust.core.msgs.DeadLetter;
import com.mentalresonance.dust.core.system.exceptions.ActorSelectionException;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 通过路径标识的Actor位置信息容器
 */
@Slf4j
public class ActorSelection implements Serializable {
    /**
     * 完全展开的路径
     */
    @Setter
    String path;

    /**
     * ActorSystem上下文
     */
    @Setter
    ActorContext context;

    /**
     * 路径是否指向远程Actor
     */
    @Setter
    boolean remote = false;

    /**
     * Actor是否已知死亡？
     */
    @Setter
    boolean dead = false;

    /**
     * 此选择的ActorRef
     */
    ActorRef target = null;

    /**
     * 由此选择确定的Actor的父级引用
     */
    ActorRef parent;

    /**
     * 创建ActorSelection
     */
    public ActorSelection() {}

    /**
     * 与actorRef.tell()语义相同
     *
     * @param msg 要发送的消息
     * @param sender 消息的发送者
     * @throws ActorSelectionException 选择的所有其他失败情况
     * @throws InterruptedException 如果目标被中断
     */
    public void tell(Serializable msg, ActorRef sender) throws ActorSelectionException, InterruptedException {
        if (dead) {
            context.deadletterActor.tell(new DeadLetter(msg, path, sender), sender);
        }
        else if (remote) {
            getRef().tell(msg, sender);
        }
        else if (! path.endsWith("*")) {
            getRef().tell(msg, sender);
        }
        else { // 我们是一个广播
            path = path.substring(0, path.length() - 2);
            parent = context.actorSelection(path);
            parent.tell(new Actor._ChildProxyMsg(msg), sender);
        }
    }

    /**
     * 获取位于选择位置的Actor的引用
     * @return 匹配的ActorRef
     * @throws ActorSelectionException 所有其他失败情况
     * @throws InterruptedException 如果被中断
     */
    public ActorRef getRef() throws ActorSelectionException, InterruptedException {
        if (remote) {
            return new ActorRef(path, context, null);
        }
        else if (null == target) {
            target = context.actorSelection(path);
        }
        return target;
    }
}