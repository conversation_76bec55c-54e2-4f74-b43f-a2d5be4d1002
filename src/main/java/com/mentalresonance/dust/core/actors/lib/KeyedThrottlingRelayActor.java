/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.*;
import com.mentalresonance.dust.core.msgs.ProxyMsg;
import com.mentalresonance.dust.core.system.exceptions.ActorInstantiationException;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 管理由代理消息中的key()索引的节流Actor集合。
 * 如果key()为null或未注册，则消息将发送到默认节流Actor。
 *
 *  <AUTHOR>
 */
@Slf4j
public class KeyedThrottlingRelayActor extends Actor {

    final Long defaultIntervalMS;
    final HashMap<String, ActorRef> throttlers = new HashMap<>();
    final HashMap<String, Long> initThrottlers;
    ActorRef defaultThrottler;

    /**
     * 创建props
     * @param defaultIntervalMS - 默认节流器的节流速率
     * @param throttlers - key -> 节流器间隔的映射
     * @return PRops
     */
    public static Props props(Long defaultIntervalMS, Map<String, Long> throttlers) {
        return Props.create(KeyedThrottlingRelayActor.class, defaultIntervalMS, throttlers);
    }

    /**
     * 构造函数
     * @param defaultIntervalMS 消息发送的最小间隔
     * @param throttlers 节流器key -> 节流器的映射
     */
    public KeyedThrottlingRelayActor(Long defaultIntervalMS, Map<String, Long> throttlers) {
        this.defaultIntervalMS = defaultIntervalMS;
        this.initThrottlers = new HashMap<>(throttlers);
    }

    @Override
    public void preStart() throws ActorInstantiationException {
        defaultThrottler = actorOf(ThrottlingRelayActor.props(defaultIntervalMS), "defaultthrottler");
        for(String key: initThrottlers.keySet()) {
            ActorRef throttler = actorOf(ThrottlingRelayActor.props(initThrottlers.get(key)));
            throttlers.put(key, throttler);
        }

    }
    /**
     * 默认行为
     * @return {@link ActorBehavior}
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            if (Objects.requireNonNull(message) instanceof ProxyMsg msg) {
                String key = msg.key();
                ActorRef throttler = (null == key) ? defaultThrottler : throttlers.getOrDefault(key, defaultThrottler);
                throttler.tell(msg, sender);
            } else {
                log.error(String.format("%s 收到非代理消息 %s", self.path, message));
            }
        };
    }
}