/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib.entities;

import com.mentalresonance.dust.core.actors.*;
import com.mentalresonance.dust.core.actors.lib.entities.msgs.DeadLetterProxyMsg;
import com.mentalresonance.dust.core.actors.lib.entities.msgs.RegisterPodDeadLettersMsg;
import com.mentalresonance.dust.core.msgs.CreateChildMsg;
import com.mentalresonance.dust.core.msgs.CreatedChildMsg;
import com.mentalresonance.dust.core.msgs.Terminated;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;

/**
 * 管理相同子级的Pod。响应CreateChildMSg通过创建命名的子级并可能传递消息来处理。
 *
 * 注意 - 这是PodManagerActor的非持久化版本，在开发过程中很有用
 *
 *  <AUTHOR>
 */
@Slf4j
public class VolatilePodManagerActor extends Actor {

    final Props childProps;
    final HashMap<String, Boolean> kids = new HashMap<>();
    final ActorRef podDeadLetterRef;

    /**
     * 创建props
     * @param childProps 创建子级的props
     * @param podDeadLetterActor 指向pod死信Actor的引用
     * @return Props
     */
    public static Props props(Props childProps, ActorRef podDeadLetterActor) {
        return Props.create(VolatilePodManagerActor.class, childProps, podDeadLetterActor);
    }
    /**
     * 创建props
     * @param childProps 创建子级的props
     * @return Props
     */
    public static Props props(Props childProps) {
        return Props.create(VolatilePodManagerActor.class, childProps, null);
    }

    /**
     * 构造函数
     * @param childProps 创建子级的props
     * @param podDeadLetterActor 指向pod死信Actor的引用
     */
    public VolatilePodManagerActor(Props childProps, ActorRef podDeadLetterActor) {
        this.childProps = childProps;
        this.podDeadLetterRef = podDeadLetterActor;
    }

    @Override
    protected void preStart() throws Exception {
        super.preStart();
        if (null != podDeadLetterRef) {
            podDeadLetterRef.tell(new RegisterPodDeadLettersMsg(), self);
        }
    }
    /**
     * 默认行为
     * @return {@link ActorBehavior}
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch(message) {
                case Terminated ignored -> kids.remove(sender.name);

                case CreateChildMsg msg -> {
                    String name = msg.getName();
                    ActorRef child = actorOf(childProps, name);
                    kids.put(name, true);
                    if (null != msg.getMsg()) {
                        child.tell(msg.getMsg(), sender);
                    }
                    if (null != sender) {
                        sender.tell(new CreatedChildMsg(name), self);
                    }
                }

                case DeadLetterProxyMsg msg -> self.tell(new CreateChildMsg(msg.name, msg.msg), msg.sender);

                // 在PodDeadLetterActor注册的确认 - 忽略
                case RegisterPodDeadLettersMsg ignored -> {}

                default -> super.createBehavior().onMessage(message);
            }
        };
    }
}