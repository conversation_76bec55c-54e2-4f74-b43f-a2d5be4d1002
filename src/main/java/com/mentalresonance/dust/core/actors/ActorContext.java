/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.mentalresonance.dust.core.system.exceptions.ActorInstantiationException;
import com.mentalresonance.dust.core.system.exceptions.ActorSelectionException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import com.mentalresonance.dust.core.system.SystemActor;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Actor上下文：管理以根Actor为根的Actor树
 *
 * ActorContext是Actor系统的核心管理组件，负责：
 * 1. 管理Actor的层次结构和路径解析
 * 2. 提供Actor的创建和查找服务
 * 3. 缓存Actor引用和构造函数以提高性能
 * 4. 管理系统级Actor的引用
 * 5. 支持远程Actor的路径解析
 *
 * 主要功能：
 * - Actor创建：通过Props配置创建新的Actor实例
 * - 路径解析：将Actor路径转换为ActorRef引用
 * - 缓存管理：缓存常用的Actor引用和构造函数
 * - 系统服务：提供死信处理等系统级服务
 *
 * <AUTHOR>
 */
@Slf4j
public class ActorContext {

    /**
     * Guardian Actor引用：Actor层次结构的根节点 '/'
     */
    @Setter
    ActorRef guardianActor;

    /**
     * User Actor引用：所有用户Actor的父节点 '/user'
     */
    ActorRef userActor;

    /**
     * Actor系统引用：此上下文所属的Actor系统
     */
    @Getter
    final ActorSystem system;

    /**
     * 主机上下文：如果启用远程通信，格式为 dust://localhost:port/actor-system
     */
    public String hostContext = null;

    /**
     * 死信Actor引用：处理无法投递消息的系统Actor '/system/deadletters'
     */
    ActorRef deadletterActor;

    /**
     * 路径解析缓存：缓存路径字符串到ActorRef的映射，提高查找性能
     * 最大缓存16384个路径映射
     */
    final Cache<String, ActorRef> resolvePaths = Caffeine.newBuilder()
        .maximumSize(16384)
        .build();

    /**
     * 构造函数：创建Actor上下文
     *
     * @param system 此上下文所属的{@link ActorSystem}
     */
    public ActorContext(ActorSystem system) {
        this.system = system;
    }

    /**
     * Actor构造函数缓存：自加载缓存，按需获取构造函数，纯粹为了提高速度
     *
     * 键：List<Class> 格式为 [ActorClass, Arg1Class, Arg2Class, ...]
     * 值：匹配此签名的构造函数
     *
     * 这个缓存避免了重复的反射操作，显著提高Actor创建的性能。
     */
    public final LoadingCache<List<Class<?>>, Constructor> actorConstructors = Caffeine.newBuilder()
            .maximumSize(4096)
            .build(k -> constructorFor(k));

    /**
     * 查找合适的构造函数：当构造函数未被缓存时调用
     *
     * 接受类列表 [ActorClass, Arg1Class, Arg2Class..] 并找到合适的构造函数。
     * 支持参数类型的自动装箱和继承关系匹配。
     *
     * @param classAndArgs 定义调用的（已装箱的）类列表
     * @return 合适的构造函数
     * @throws Exception 如果无法找到合适的构造函数
     */
    private Constructor<?> constructorFor(List<Class<?>> classAndArgs) throws Exception {
        try {
            List<Constructor<?>> constructors = Arrays.stream(classAndArgs.get(0).getConstructors()).toList();
            List<Class<?>> argClasses = classAndArgs.size() > 1 ? classAndArgs.subList(1, classAndArgs.size()) : List.of();
            List<Constructor> matchingConstructors = new ArrayList<>();

            for (Constructor cons : constructors ) {
                List<Class<?>> parameterTypes = autoboxPrimitiveTypes(cons.getParameterTypes());
                if (areParametersMatching(parameterTypes, argClasses)) {
                    return cons;
                }
            }

            throw new Exception("No constructor found for " + classAndArgs.get(0));
        }
        catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 检查构造函数的参数类型是否与参数的类匹配
     *
     * 这个方法用于验证构造函数签名是否与提供的参数类型兼容，
     * 支持继承关系的匹配。
     *
     * @param parameterTypes 构造函数的参数类型
     * @param argsClasses 参数的类型
     * @return 如果匹配返回true，否则返回false
     */
    private static boolean areParametersMatching(List<Class<?>> parameterTypes, List<Class<?>> argsClasses) {
        if (parameterTypes.size() != argsClasses.size()) {
            return false;
        }
        for (int i = 0; i < parameterTypes.size(); i++) {
            Class<?> clazz = argsClasses.get(i);
            if (clazz != null && !(parameterTypes.get(i).isAssignableFrom(clazz))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 自动装箱基本类型
     *
     * 基本类型（！）无法通过isAssignableFrom检查，所以如有必要在这里装箱它们。
     * 这确保了构造函数匹配能够正确处理基本类型参数。
     *
     * @param classList 类型数组 - 可能包含一些基本类型
     * @return 类型列表 - 必要时已装箱
     */
    private  List<Class<?>> autoboxPrimitiveTypes(Class<?>[] classList) {
        List<Class<?>> autoboxedList = new ArrayList<>();

        for (Class<?> clazz : classList) {
            if (clazz.isPrimitive()) {
                // 将基本类型转换为对应的包装类
                if (clazz == int.class) {
                    autoboxedList.add(Integer.class);
                } else if (clazz == boolean.class) {
                    autoboxedList.add(Boolean.class);
                } else if (clazz == byte.class) {
                    autoboxedList.add(Byte.class);
                } else if (clazz == char.class) {
                    autoboxedList.add(Character.class);
                } else if (clazz == double.class) {
                    autoboxedList.add(Double.class);
                } else if (clazz == float.class) {
                    autoboxedList.add(Float.class);
                } else if (clazz == long.class) {
                    autoboxedList.add(Long.class);
                } else if (clazz == short.class) {
                    autoboxedList.add(Short.class);
                } else if (clazz == void.class) {
                    autoboxedList.add(Void.class);
                }
            } else {
                // 如果不是基本类型，直接添加类
                autoboxedList.add(clazz);
            }
        }

        return autoboxedList;
    }

    /**
     * 将路径解析为ActorRef
     *
     * 路径必须以'/'为根。这个方法实现了智能的路径解析策略，
     * 使用缓存和反向查找来优化性能。
     *
     * @param path 完整路径
     * @return ActorRef引用
     * @throws InterruptedException 如果被中断
     * @throws ActorSelectionException 所有其他失败情况
     */
    public ActorRef actorSelection(String path) throws InterruptedException,  ActorSelectionException {
        ActorRef ref = null;

        try {
            if (! path.endsWith("/"))
                path = path + "/";

            // 首先检查缓存
            if (null != (ref = resolvePaths.getIfPresent(path))) {
                return ref;
            }
            else if (path.contains(":")) {
                // 远程Actor引用
                ActorRef remoteRef =  new ActorRef(path, this, null);
                resolvePaths.put(path, remoteRef);
                return remoteRef;
            }
            else  {
                /*
                 * 现在我们需要通过询问父Actor关于其子Actor来解析。与其总是从guardian开始，
                 * 我们向后工作，沿着路径向上找到我们有引用的最近的Actor，然后从那里开始。
                 *
                 * 例如，假设我们想解析/a/b/c/d/，而我们已经解析了b，那么我们想发送一个请求
                 * 来解析/c/d到/a/b。
                 *
                 */

                // Java从数组获取可变列表的方式 :(
                List<String> parts = Stream.of(path.split("/")).collect(Collectors.toCollection(ArrayList::new));
                List<String> post = new LinkedList<>();
                int left;

                // 反向查找最近的已缓存引用
                while (((left = parts.size()) != 0) && null == ref) {
                    String lastPart = parts.remove(left - 1);
                    post.add(lastPart);
                    ref = resolvePaths.getIfPresent(String.join("/", parts) + "/");
                }
                if (0 == left) {
                    ref = guardianActor;
                }
                // 现在post是Actor路径段的列表，ref是第一个段的父Actor。
                // 注意post会从最后的"/"中获取一个尾随的""。另外列表的顺序与我们需要的相反...
                if (post.getLast().isEmpty())
                    post.removeLast();
                Collections.reverse(post);
                log.trace("从{}解析{}", ref, path);

                // 使用较长的超时时间来处理启动问题
                ActorRef resolved = null;

                try {
                    resolved = ref.resolve(post).get(10, TimeUnit.SECONDS);
                } catch (TimeoutException e) {
                    log.warn("解析{}超时", path);
                } catch (Exception e) {
                    log.warn("解析{}时发生错误：{}", path, e.getMessage());
                }

                /*
                 * 是否缓存死信。我们不缓存，因为如果Actor被（重新）启动，
                 * 我们必须刷新缓存...代价是重复的解析可能会失败 - 这个代价
                 * （到目前为止）是值得的。
                 */
                if (null == resolved) {
                    log.trace("{}未找到", path);
                    resolved = deadLetterRef(path);
                } else {
                    log.trace("{}已解析", path);
                    resolvePaths.put(path, resolved);
                }

                return resolved;
            }
        }
        catch (InterruptedException x) {
            throw x;
        }
        catch (Exception e) {
            e.printStackTrace();
            throw new ActorSelectionException(e.getMessage());
        }
    }

    /**
     * 创建死信引用：为不存在的路径创建指向死信Actor的引用
     *
     * @param path 不存在的Actor路径
     * @return 指向死信Actor的引用
     * @throws ActorSelectionException 选择异常
     * @throws InterruptedException 中断异常
     */
    ActorRef deadLetterRef(String path) throws ActorSelectionException, InterruptedException {
        ActorRef deadLetter = getDeadLetterActor();
        ActorRef ref = new ActorRef(path, this, deadLetter.actor);
        ref.isDeadLetter = true;
        ref.thread = deadLetter.thread;
        ref.mailBox = deadLetter.mailBox;
        return ref;
    }

    /**
     * 通过中断邮箱来停止指定引用的Actor
     *
     * @param ref 要停止的Actor
     * @param cause 在停止的引用中设置isException
     */
    public void stop(ActorRef ref, Throwable cause) {
        ref.isException = cause;
        ref.thread.interrupt();
    }

    /**
     * 默认停止方法 - 不提供原因
     *
     * @param ref 要停止的Actor引用
     */
    public void stop(ActorRef ref) {
        stop(ref, null);
    }

    /**
     * 关闭整个系统
     */
    public void stop() { system.stop(); }

    /**
     * 当Actor停止时调用，从路径解析缓存中移除它
     *
     * @param path 已停止Actor的路径
     */
    public void decache(String path) {
        resolvePaths.invalidate(path);
    }

    /**
     * 将路径添加到缓存
     *
     * 当解析"./...."时我们需要这个，以确保我们不会向正在进行解析的Actor
     * 发送_ResolveMsg（这会阻塞解析）。
     *
     * @param path Actor路径
     * @param ref Actor引用
     */
    public void encache(String path, ActorRef ref) {
        resolvePaths.put(path, ref);
    }

    /**
     * 获取/user引用
     *
     * 用户Actor是所有用户创建的Actor的父Actor。
     *
     * @return /user引用
     * @throws InterruptedException 如果被中断
     * @throws ActorSelectionException 发生异常时
     */
    public ActorRef getUserActor() throws InterruptedException, ActorSelectionException {
        if (null == userActor)
            userActor = actorSelection("/user");
        return userActor;
    }

    /**
     * 获取/system/deadletters引用
     *
     * 死信Actor处理发送给不存在Actor的消息。
     *
     * @return /system/deadletters引用
     * @throws InterruptedException 如果被中断
     * @throws ActorSelectionException 发生异常时
     */
    public ActorRef getDeadLetterActor() throws InterruptedException, ActorSelectionException {
        if (null == deadletterActor)
            deadletterActor = actorSelection("/system/" + SystemActor.DEAD_LETTERS);
        return deadletterActor;
    }

    /**
     * 使用随机名称在/user下从props创建Actor
     *
     * @param props Actor配置
     * @return 创建的Actor的ActorRef引用
     * @throws ActorInstantiationException 发生错误时
     */
    public ActorRef actorOf(Props props) throws ActorInstantiationException {
        return actorOf(props, UUID.randomUUID().toString());
    }

    /**
     * 使用指定名称在/user下从props创建Actor
     *
     * 这个方法通过向用户Actor发送创建子Actor的消息来实现Actor创建。
     * 使用CompletableFuture来同步等待创建完成。
     *
     * @param props Actor配置
     * @param name 要使用的名称
     * @return 创建的Actor的ActorRef引用
     * @throws ActorInstantiationException 发生错误时
     */
    public ActorRef actorOf(Props props, String name) throws ActorInstantiationException {
        try {
            var ref = getUserActor();
            var msg = new _CreateChildMsg(props, name);

            ref.props = props;
            ref.tell(msg, null);
            return msg.ref.get();
        }
        catch (Exception e) {
            log.error(e.getMessage());
            throw new ActorInstantiationException();
        }
    }

    /**
     * 用于context.actorOf()的'硬编码'创建子Actor消息
     *
     * 这是一个内部消息类，用于在ActorContext和用户Actor之间
     * 传递创建子Actor的请求。
     */
    static class _CreateChildMsg implements Serializable {
        /**
         * Actor配置：用于创建Actor的Props
         */
        @Getter
        final Props props;

        /**
         * Actor名称：新Actor的名称
         */
        @Getter
        final String name;

        /**
         * 异步引用：用于返回创建的ActorRef的Future
         */
        @Getter
        final CompletableFuture<ActorRef> ref;

        /**
         * 构造函数：创建子Actor消息
         *
         * @param props Actor配置
         * @param name Actor名称
         */
        public _CreateChildMsg(Props props, String name) {
            this.props = props;
            this.name = name;
            this.ref = new CompletableFuture<>();
        }
    }
}
