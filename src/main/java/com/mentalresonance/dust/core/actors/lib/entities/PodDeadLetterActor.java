/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib.entities;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.actors.lib.entities.msgs.DeadLetterProxyMsg;
import com.mentalresonance.dust.core.actors.lib.entities.msgs.RegisterPodDeadLettersMsg;
import com.mentalresonance.dust.core.msgs.*;
import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.List;

/**
 * 拦截死信。如果死信是发送给已注册路径上的Actor的子级，
 * 则将消息包装在{@link DeadLetterProxyMsg}中并发送给预期接收者的父级。
 *
 * 通常接收者将是{@link PodManagerActor}，它将创建命名的子级并将消息重新提交给它。
 *
 * 在创建此机制时（通过{@link RegisterPodDeadLettersMsg}），我们可以提供一个（可选的）类集合。
 * 如果此集合非空，则仅传递此集合中的消息。
 *
 * <AUTHOR>
 */
@Slf4j
public class PodDeadLetterActor extends Actor {

    final HashMap<String, List<Class<?>>> pathCache = new HashMap<>();

    /**
     * props
     * @param paths [路径, [触发类]]列表
     * @return 此Actor的Props
     */
    public static Props props(List<List> paths) {
        return Props.create(PodDeadLetterActor.class, paths);
    }

    /**
     * 构造函数
     * @param paths [路径, [触发类]]列表
     */
	public PodDeadLetterActor(List<List> paths) {
        for (List<?> path :paths) {
            pathCache.put(path.get(0).toString(), (List<Class<?>>)path.get(1));
        }
    }

    @Override
    protected void preStart() throws Exception {
        super.preStart();

        PubSubMsg sub = new PubSubMsg(DeadLetter.class);
        try {
            context.getDeadLetterActor().tell(sub, self);
        }
        catch (Exception e) {
            log.error("无法订阅DeadLetterActor: %s".formatted(e.getMessage()));
        }
    }

    /**
     * 默认行为
     * @return {@link ActorBehavior}
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch(message) {
                case DeadLetter dl -> {
                    String recipientPath = normalizePath(dl.getPath());
                    String senderPath = null != dl.getSender() ? dl.getSender().path : null;
                    String recipientParentPath = recipientPath.substring(0, recipientPath.lastIndexOf('/'));

                    /*
                     * 我们不启动一个Actor来接受它自己发送的消息！当我们在队列中仍有消息时停止自己时会发生这种情况。
                     * 它们会转到死信然后到这里。但我停止了自己！！
                     */
                    if (recipientPath.equals(senderPath)) {
                        return;
                    }

                    /*
                     * 忽略不应重新调用Actor的死信
                     */
                    switch (dl.getMessage()) {
                        case SnapshotSuccessMsg ignored:
                            break;
                        case SnapshotFailureMsg ignored:
                            break;
                        case DeleteSnapshotFailureMsg ignored:
                            break;
                        case DeleteSnapshotSuccessMsg ignored:
                            break;

                        default:
                            List<Class<?>> acceptedMessages;

                            if (null != (acceptedMessages = pathCache.get(recipientParentPath))) {
                                // 接收者的父级已在我这里注册
                                if (acceptedMessages.isEmpty() || acceptedMessages.contains(dl.getMessage().getClass())) {
                                    actorSelection(recipientParentPath).tell(
                                        new DeadLetterProxyMsg(nameFromPath(recipientPath), dl.getMessage(), dl.getSender()),
                                        dl.getSender()
                                    );
                                }
                                else // Todo: 这实际上不是一个警告 - 它在执行其工作。但现在用于调试
                                    log.warn("死信处理器中未处理的消息类型 {} 到:{} 从:{} 消息:{}",
                                        dl.getMessage().getClass(),
                                        recipientPath,
                                        senderPath,
                                        dl
                                    );
                            }
                            else {
                                if (!(dl.getMessage() instanceof ZombieMsg)) {
                                    log.warn("未处理的死信 到:{} 从:{} 消息:{}。{} 是否已注册？",
                                            recipientPath,
                                            senderPath,
                                            dl,
                                            recipientParentPath
                                    );
                                }
                            }
                    }
                }
                /*
                 * 注册发送者并返回其消息作为确认。
                 */
                case RegisterPodDeadLettersMsg msg -> {
                    log.trace(
                        "{} 正在向 {} 注册。消息={}",
                        sender.path,
                        self.path,
                        null != msg.acceptedMessages ? msg.acceptedMessages.toString() : ""
                    );
                    pathCache.put(normalizePath(sender.path), msg.acceptedMessages);
                    sender.tell(msg, self);
                }

                default -> super.createBehavior().onMessage(message);
            }
        };
    }

    private String normalizePath(String s) {
        if (s.endsWith("/"))
            s = s.substring(0, s.length() - 1);
        return s;
    }

    private String nameFromPath(String s) {
        return s.substring(1 + s.lastIndexOf('/'));
    }
}