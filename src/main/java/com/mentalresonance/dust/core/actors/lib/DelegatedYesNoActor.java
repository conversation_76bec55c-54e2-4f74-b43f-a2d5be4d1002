/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.msgs.DelegatingMsg;
import com.mentalresonance.dust.core.msgs.YesNoMsg;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 正如其名。等待接收一个字符串，将其解析为是或否，
 * 并将响应作为YesNoMsg发送回其委托者。忽略所有其他消息
 *
 *  <AUTHOR>
 */
@Slf4j
public class DelegatedYesNoActor extends Actor {

    final ActorRef delegator;

    /**
     * 创建props
     * @param delegator 显式委托者。如果为null，则假定委托者是父级
     * @return Props
     */
    public static Props props(ActorRef delegator) {
        return Props.create(DelegatedYesNoActor.class, delegator);
    }

    /**
     * 创建props。假定委托者是父级
     * @return Props
     */
    public static Props props() {
        return Props.create(DelegatedYesNoActor.class, (Object) null);
    }

    /**
     * 构造函数
     * @param delegator 委托者（或null）
     */
    public DelegatedYesNoActor(ActorRef delegator) {
        this.delegator = delegator;
    }
    /**
     * 默认行为
     * @return {@link ActorBehavior}
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            if (Objects.requireNonNull(message) instanceof String msg) {
                DelegatingMsg dm = new DelegatingMsg(new YesNoMsg(msg.trim().equalsIgnoreCase("yes")));
                if (null != delegator)
                    delegator.tell(dm, self);
                else
                    parent.tell(dm, self);
                stopSelf();
            }
        };
    }
}