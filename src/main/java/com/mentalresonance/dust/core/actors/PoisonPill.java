/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import java.io.Serializable;

/**
 * 毒丸消息：Actor处理此消息时会导致其执行stop(self)
 *
 * 注意：这只是一个常规消息，只有当从接收者的邮箱中取出时才会停止Actor。
 *
 * 工作原理：
 * 1. 毒丸消息被放入Actor的邮箱队列中
 * 2. Actor按顺序处理消息，直到处理到毒丸消息
 * 3. 处理毒丸消息时，Actor调用stop(self)开始停止流程
 * 4. 确保在停止前处理完所有之前的消息
 *
 * 使用场景：
 * 1. 优雅关闭：确保Actor处理完所有待处理消息后再停止
 * 2. 资源清理：在停止前完成必要的清理工作
 * 3. 有序停止：在分布式系统中按特定顺序停止Actor
 * 4. 测试场景：在测试中控制Actor的生命周期
 *
 * 优势：
 * - 有序处理：保证消息处理的顺序性
 * - 优雅关闭：避免强制终止导致的数据丢失
 * - 简单易用：不需要额外的参数或配置
 * - 标准化：提供统一的Actor停止机制
 *
 * <AUTHOR>
 */
public class PoisonPill implements Serializable {
    /**
     * 默认构造函数：创建毒丸消息实例
     *
     * 毒丸消息不需要携带额外的数据，仅作为停止信号使用。
     */
    public PoisonPill() {}
}

