/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.msgs.CompletionRequestMsg;
import com.mentalresonance.dust.core.msgs.ReturnableMsg;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 完成服务Actor：处理异步完成请求的服务Actor
 *
 * 接收CompletionRequestMsg的子类消息，保留Future对象并将消息发送到目标Actor。
 * 收到的第一个回复消息将用于设置Future并停止自己。
 *
 * 这是一个服务Actor，应该在{@link ServiceManagerActor}下运行。
 *
 * 主要功能：
 * 1. 处理异步完成请求
 * 2. 管理CompletableFuture的生命周期
 * 3. 支持消息的透传和路由
 * 4. 提供超时保护机制
 *
 * 使用场景：
 * - 异步服务调用的Future包装
 * - 请求-响应模式的实现
 * - 超时控制的异步操作
 *
 * <AUTHOR>
 */
@Slf4j
public class CompletionServiceActor extends Actor {

    /**
     * 完成Future：用于异步返回结果给调用者
     */
    CompletableFuture<Object> future;

    /**
     * 最大等待时间：超时后自动完成Future并停止
     */
    Long maxTime;

    /**
     * 创建Actor的Props配置（使用默认30秒超时）
     *
     * @return Props配置对象
     */
    public static Props props() {
        return Props.create(CompletionServiceActor.class, 30000L);
    }

    /**
     * 创建Actor的Props配置（自定义超时时间）
     *
     * @param maxTime 最大等待时间（毫秒）
     * @return Props配置对象
     */
    public static Props props(Long maxTime) {
        return Props.create(CompletionServiceActor.class, maxTime);
    }

    /**
     * 构造函数：创建完成服务Actor
     *
     * @param maxTime 最大等待时间（毫秒）
     */
    public CompletionServiceActor(Long maxTime) {
        this.maxTime = maxTime;
    }

    /**
     * 启动前初始化：设置超时保护
     */
    @Override
    protected void preStart() {
        dieIn(maxTime); // 设置死人开关，防止无限等待
    }

    /**
     * 超时死亡处理：完成Future并执行清理
     */
    @Override
    protected void dying() {
        future.complete(null); // 超时时返回null
        super.dying();
    }

    /**
     * 创建默认行为：处理完成请求和响应消息
     *
     * @return 消息处理行为
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            if (Objects.requireNonNull(message) instanceof CompletionRequestMsg msg) {
                // 处理完成请求消息
                log.trace("收到CompletionRequestMsg：{}，从{}发送到{}", msg, sender, msg.target);
                Serializable passThrough = msg.getPassThroughMsg();
                future = msg.getFuture(); // 保存Future引用
                msg.setSender(self); // 设置发送者为自己

                if (null != passThrough) {
                    // 如果有透传消息，发送透传消息而不是请求消息本身
                    if (passThrough instanceof ReturnableMsg) {
                        // 如果是可返回消息（如管道返回），确保回复到自己
                        ((ReturnableMsg) passThrough).setSender(self);
                    }
                    msg.target.tell(passThrough, self);
                } else {
                    // 没有透传消息，直接发送请求消息
                    msg.target.tell(msg, self);
                }
            } else {
                // 处理响应消息：完成Future并停止自己
                log.trace("收到来自{}的响应。正在完成。", sender);
                future.complete(message);
                stopSelf();
            }
        };
    }
}
