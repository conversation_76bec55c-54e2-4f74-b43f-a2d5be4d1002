/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import lombok.Getter;

/**
 * Cancellable只是线程的包装器。
 *
 *  <AUTHOR>
 */
@Getter
public class Cancellable {

    final Thread thread;

    /**
     * 包装线程
     * @param thread 要包装的线程
     */
    public Cancellable(Thread thread) { this.thread = thread; }

    /**
     * 通过中断其线程来取消Cancellable
     */
    public void cancel() {
        try {
            thread.interrupt();
        } catch (Exception ignored) {}
    }
}