/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

/**
 * 如果一个Actor因为抛出异常而停止，子Actor树将被给予这个异常，
 * 就像它们自己抛出异常一样。因此，子Actor可以根据此异常调整其postStop()行为
 * （例如，如果是一个持久化Actor，则不删除状态）。
 *
 * 所以如果一个Actor看到这个异常，它就知道'cause'是由祖先抛出的。
 */
package com.mentalresonance.dust.core.actors;

import lombok.Getter;

@Getter
public class ParentException extends Exception {

    /**
     * 原始罪因
     */
    Throwable cause;

    public ParentException(Throwable cause) {
        this.cause = cause;
    }
}