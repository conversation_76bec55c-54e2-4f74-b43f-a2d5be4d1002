/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.system.exceptions.ActorInstantiationException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 管道中的一个阶段，作为一组其他Actor的分发点，这些Actor是它的子级。如果它
 * 从父级接收到消息，它会将该消息发送给所有子级。如果消息发送者
 * 不是父级，它会将其发送给父级。因此，一组Actor可以像管道中的单个阶段一样行为。
 *
 * 最常用于管道的开头 - 例如一些独立的源Actor（如RSS）。每个都填充管道
 * 就好像它是该阶段的唯一Actor一样。
 *
 * 从子级发送给父级的消息标记为来自hub。
 *
 * 注意：如果在hub下运行的Actor是持久化的，则应提供名称，
 * 否则由于名称可能更改，将找不到任何持久化状态。
 */
@Slf4j
public class PipelineHubActor extends Actor {

    final List<Props> actors;
    final List<String> names;

    /**
     * 创建Props
     * @param actors 在hub上创建Actor的Actor Props列表
     * @param names 这些Actor的名称（按顺序）
     * @return Props
     */
    public static Props props(List<Props> actors, List<String> names) {
        return Props.create(PipelineHubActor.class, actors, names);
    }

    /**
     * 创建Props
     * @param actors 在hub上创建Actor的Actor Props列表。获得唯一名称
     * @return Props
     */
    public static Props props(List<Props> actors) {
        return Props.create(PipelineHubActor.class, actors, null);
    }

    /**
     * 构造函数
     * @param actors 在hub上创建Actor的Actor Props列表
     * @param names 这些Actor的名称（按顺序）
     */
    public PipelineHubActor(List<Props> actors, List<String> names) {
        this.actors = actors;
        this.names = names;
    }

    @Override
    protected void preStart() throws ActorInstantiationException {
        for (int i = 0; i < actors.size(); ++i)
        {
            Props actor = actors.get(i);
            if (null != names) {
                actorOf(actor, names.get(i));
            } else
                actorOf(actor);
        }
    }

    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            if (sender == parent) {
                for(ActorRef child: getChildren()) {
                    child.tell(message, sender);
                }
            } else
                parent.tell(message, self);
        };
    }
}