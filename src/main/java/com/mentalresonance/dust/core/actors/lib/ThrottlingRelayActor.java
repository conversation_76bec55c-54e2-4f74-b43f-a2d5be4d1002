/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.Cancellable;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.msgs.ProxyMsg;
import com.mentalresonance.dust.core.msgs.StartMsg;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.LinkedBlockingQueue;

/**
 * 简单的节流Actor。接收代理消息，每隔intervalMS发送一个。
 *
 *  <AUTHOR>
 */
@Slf4j
public class ThrottlingRelayActor extends Actor {

    final Long intervalMS;
    final LinkedBlockingQueue<ProxyMsg> q = new LinkedBlockingQueue<>();
    final StartMsg START = new StartMsg();

    Cancellable pump;

    /**
     * Props
     * @param intervalMS - 发送尝试之间的时间间隔（毫秒）
     * @return Props
     */
    public static Props props(Long intervalMS) {
        return Props.create(ThrottlingRelayActor.class, intervalMS);
    }

    /**
     * 构造函数
     * @param intervalMS 发送消息的最小时间间隔（毫秒）
     */
    public ThrottlingRelayActor(Long intervalMS) {
        this.intervalMS = intervalMS;
    }

    @Override
    public void preStart() {
        pump = scheduleIn(new StartMsg(), intervalMS);
    }

    @Override
    public void postStop() {
        pump.cancel();
    }


    /**
     * 默认行为
     * @return {@link ActorBehavior}
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch(message) {
                case StartMsg msg -> {
                    ProxyMsg p;

                    if (null != (p = q.poll()))
                    {
                        p.setProxied(true);
                        if (null != p.target)
                            p.target.tell(p, p.getSender());
                        else
                            p.getSender().tell(p, p.getSender());
                    }
                        pump = scheduleIn(msg, intervalMS);
                    }
                case ProxyMsg msg -> {
                    q.add(msg);
                }

                default -> log.error(String.format("%s 收到非代理消息 %s", self.path, message));
            }
        };
    }
}