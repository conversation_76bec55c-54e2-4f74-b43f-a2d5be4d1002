/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib.entities.msgs;

import lombok.Getter;

import java.io.Serializable;

/**
 * 实体状态消息：发送给等待接收状态的实体以获取其状态
 *
 * 这个消息用于实体状态的传递和恢复，通常在以下场景中使用：
 * 1. 实体初始化时接收初始状态
 * 2. 实体恢复时接收持久化状态
 * 3. 实体迁移时传递状态数据
 * 4. 状态同步和备份操作
 *
 * 泛型设计使得可以传递任意类型的状态对象，提供了类型安全性。
 *
 * @param <T> 状态对象的类型
 * <AUTHOR>
 */
@Getter
public class EntityStateMsg<T> implements Serializable {
    /**
     * 恢复的状态对象：包含实体的完整状态信息
     */
    final T state;

    /**
     * 构造函数：创建实体状态恢复消息
     *
     * @param state 要恢复的状态对象
     */
    public EntityStateMsg(T state) { this.state = state; }
}
