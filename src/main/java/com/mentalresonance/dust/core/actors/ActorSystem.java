/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import com.mentalresonance.dust.core.net.CoreTCPObjectServer;
import com.mentalresonance.dust.core.services.PersistenceService;
import com.mentalresonance.dust.core.services.SerializationService;
import com.mentalresonance.dust.core.system.ActorSystemConnectionManager;
import com.mentalresonance.dust.core.system.ActorSystemConnectionManager.WrappedTCPObjectSocket;
import com.mentalresonance.dust.core.system.GuardianActor;
import com.mentalresonance.dust.core.system.exceptions.ActorInstantiationException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.nustaq.net.TCPObjectServer;
import org.nustaq.net.TCPObjectSocket;

import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.InvocationTargetException;
import java.net.URI;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Actor系统：设置Guardian Actor '/'和可选的dust:服务器
 *
 * ActorSystem是整个Actor框架的核心容器，负责：
 * 1. 管理Actor的生命周期和层次结构
 * 2. 提供Actor之间的通信基础设施
 * 3. 支持本地和远程Actor的透明通信
 * 4. 管理系统级服务（持久化、序列化等）
 * 5. 处理Actor的监督和错误恢复
 *
 * <AUTHOR>
 */
@Slf4j
public class ActorSystem {

    /**
     * 持久化服务：用于Actor状态的持久化和恢复
     * 支持多种持久化实现（Jackson、Gson、FST等）
     */
    @Getter
    @Setter
    private PersistenceService persistenceService = null;

    /**
     * ActorSystem的名称：用于标识和网络通信
     */
    @Getter
    final String name;

    /**
     * 系统名称长度：用于从路径中裁剪系统名称部分
     */
    final int systemLength;

    /**
     * Actor上下文：提供系统级的服务和配置
     */
    @Getter
    ActorContext context;

    /**
     * Guardian Actor引用：Actor层次结构的根节点
     */
    private ActorRef guardianRef;

    /**
     * 网络端口：如果启用远程通信，则监听此端口
     */
    @Getter
    Integer port = null;

    /**
     * 停止完成标志：用于异步等待系统完全停止
     */
    CompletableFuture<Boolean> haveStopped = null;

    /**
     * 全局停止标志：指示系统是否正在停止
     */
    public static boolean isStopping = false;

    /**
     * 可选的停止回调：在ActorSystem关闭后运行的回调函数
     */
    @Setter
    Runnable stopping = null;

    /**
     * TCP对象服务器：处理远程Actor通信
     */
    private CoreTCPObjectServer server = null;

    /**
     * 连接管理器：管理到远程Actor系统的连接池
     */
    final ActorSystemConnectionManager actorSystemConnectionManager;

    /**
     * 停止状态标志：防止重复停止操作
     *
     * 我们可能停止ActorSystem但稍后又收到关闭消息（通过关闭钩子，例如在测试期间），
     * 所以我们使用isStopped来确定我们确实认为已经停止了，不再尝试重复停止。
     */
    private boolean isStopped = false;

    /**
     * 创建仅本地的Actor系统
     *
     * @param name ActorSystem名称
     * @throws ActorInstantiationException 创建核心服务Actor时的异常
     * @throws IOException 创建核心服务Actor时的IO异常
     * @throws InvocationTargetException 创建核心服务Actor时的反射异常
     * @throws NoSuchMethodException 创建核心服务Actor时的方法不存在异常
     * @throws InstantiationException 创建核心服务Actor时的实例化异常
     * @throws IllegalAccessException 创建核心服务Actor时的访问异常
     */
    public ActorSystem(String name)
            throws ActorInstantiationException, IOException, InvocationTargetException,
            NoSuchMethodException, InstantiationException, IllegalAccessException {
        this(name, null, true);
    }

    /**
     * 创建仅本地的Actor系统
     *
     * @param name           ActorSystem名称
     * @param logDeadLetters 如果为true则记录死信投递日志
     * @throws ActorInstantiationException 创建核心服务Actor时的异常
     * @throws IOException 创建核心服务Actor时的IO异常
     * @throws InvocationTargetException 创建核心服务Actor时的反射异常
     * @throws NoSuchMethodException 创建核心服务Actor时的方法不存在异常
     * @throws InstantiationException 创建核心服务Actor时的实例化异常
     * @throws IllegalAccessException 创建核心服务Actor时的访问异常
     */
    public ActorSystem(String name, boolean logDeadLetters)
            throws ActorInstantiationException, IOException, InvocationTargetException,
            NoSuchMethodException, InstantiationException, IllegalAccessException {
        this(name, null, logDeadLetters);
    }

    /**
     * 创建支持远程通信的Actor系统，在指定端口上运行
     *
     * @param name 在此主机上唯一的Actor名称
     * @param port 监听端口
     * @throws InvocationTargetException 创建核心服务Actor时的反射异常
     * @throws NoSuchMethodException 创建核心服务Actor时的方法不存在异常
     * @throws InstantiationException 创建核心服务Actor时的实例化异常
     * @throws IllegalAccessException 创建核心服务Actor时的访问异常
     * @throws ActorInstantiationException 创建核心服务Actor时的异常
     */
    public ActorSystem(String name, Integer port)
            throws InvocationTargetException, NoSuchMethodException, InstantiationException,
            IllegalAccessException, ActorInstantiationException {

        this(name, port, true);
    }

    /**
     * 创建支持远程通信的Actor系统，在指定端口上运行
     *
     * @param name           在此主机上唯一的Actor名称
     * @param port           监听端口
     * @param logDeadLetters 如果为true则记录死信日志
     * @throws InvocationTargetException 创建核心服务Actor时的反射异常
     * @throws NoSuchMethodException 创建核心服务Actor时的方法不存在异常
     * @throws InstantiationException 创建核心服务Actor时的实例化异常
     * @throws IllegalAccessException 创建核心服务Actor时的访问异常
     * @throws ActorInstantiationException 创建核心服务Actor时的异常
     */
    public ActorSystem(String name, Integer port, boolean logDeadLetters)
            throws InvocationTargetException, NoSuchMethodException, InstantiationException,
            IllegalAccessException, ActorInstantiationException {

        this.name = name;
        this.port = port;
        systemLength = name.length() + 1;
        actorSystemConnectionManager = new ActorSystemConnectionManager();
        init(logDeadLetters);
        log.info("已启动ActorSystem：{} 端口：{}", name, port);
    }

    /**
     * 返回Guardian Actor所在的线程。这允许我们'join'这个线程，
     * 从而等待Actor系统完全关闭。
     *
     * @return Guardian Actor邮箱线程
     */
    public Thread systemThread() {
        return guardianRef.thread;
    }

    /**
     * 初始化Actor系统的核心组件
     *
     * @param logDeadLetters 是否记录死信日志
     */
    private void init(boolean logDeadLetters)
            throws InvocationTargetException, NoSuchMethodException, InstantiationException,
            IllegalAccessException, ActorInstantiationException {

        // 创建Actor上下文
        context = new ActorContext(this);

        // 创建Guardian Actor（根Actor）
        guardianRef = new ActorRef("", "", context,  GuardianActor.class.getDeclaredConstructor().newInstance());
        Guardian guardian = startGuardian(guardianRef);

        // 设置上下文和初始化Guardian
        context.setGuardianActor(guardianRef);
        guardian.actor.setContext(context);
        guardian.actor.init(logDeadLetters);

        // 如果指定了端口，启动网络服务器
        if (null != port) {
            try {
                context.hostContext = String.format("dust://localhost:%d/%s", port, name);
                haveStopped = runServer(port, actorSystemConnectionManager);
            } catch (IOException e) {
                log.error(String.format("Cannot start server on port %d", port));
            }
        }

        // 关闭钩子：确保系统优雅关闭
        Runtime.getRuntime().addShutdownHook(new Thread(
                () -> {
                    if (!isStopped) {
                        log.warn("收到关闭信号 - 正在停止！");
                        stop();
                    }
                }
        ));
    }

    /**
     * 启动Guardian Actor：初始化Actor层次结构的根节点
     *
     * @param ref Guardian Actor的引用
     * @return Guardian包装对象
     */
    private Guardian startGuardian(ActorRef ref) {

        Actor actor = guardianRef.actor;
        ref.mailBox = new Actor.MailBox();
        ref.thread = Thread.startVirtualThread(actor);
        ref.thread.setName("GuardianActor");
        actor.setParent(null);
        actor.setSelf(ref);

        return new Guardian(ref, (GuardianActor) actor);
    }

    /**
     * 停止Actor系统
     *
     * 如果启用了远程通信，这意味着停止服务器。
     * 如果定义了stopping回调，则最后运行它。
     *
     * <b>注意</b>：关闭是异步的，所以虽然stopping回调最后被调用，
     * 但不能保证ActorSystem已完全关闭（即整个Actor树已停止）。
     *
     * @param inShutdown 指示我们处于干净关闭状态的标志。
     *                   PersistentActor不应删除其状态。如果为false，则它们可能会删除状态。
     */
    public void stop(boolean inShutdown) {

        isStopping = true;

        PersistentActor.setInShutdown(inShutdown);

        log.info("正在停止ActorSystem：{}，端口：{}", name, port);
        if (null != port) {
            log.info("正在停止服务器");
            try {
                server.stop();
                haveStopped.get(5, TimeUnit.SECONDS);
                actorSystemConnectionManager.shutdown();
                log.info("服务器已关闭");
            }
            catch (Exception e) {
                log.error("无法停止服务器：{}", e.getMessage());
                e.printStackTrace();
            }
        }
        log.info("正在停止Guardian");
        context.stop(guardianRef);
        try {
            guardianRef.waitForDeath();
            log.info("Guardian已停止");
        } catch (Exception e) {
            log.warn("停止guardian引用时发生异常...忽略");
        }

        isStopped = true;

        if (null != stopping) {
            log.info("运行stopping()回调");
            stopping.run();
        }
        log.info("已停止");
    }

    /**
     * 默认停止方法：inShutdown为true，所以Actor将保留其状态供下次使用
     */
    public void stop() { stop(true); }

    /**
     * 在指定端口上运行服务器，上下文为/<actor-system-name>
     *
     * @param port 监听端口
     * @param actorSystemConnectionManager 连接管理器，便于清理
     * @return 服务器停止时完成的Future
     * @throws IOException IO异常
     */
    CompletableFuture<Boolean> runServer(int port, ActorSystemConnectionManager actorSystemConnectionManager) throws IOException {
        CompletableFuture<Boolean> haveStopped = new CompletableFuture<>();

        server = new CoreTCPObjectServer(
                SerializationService.getFstConfiguration(),
                port,
                actorSystemConnectionManager,
                haveStopped
        );

        this.port = port;

        server.start(new TCPObjectServer.NewClientListener() {

            @Override
            public void connectionAccepted(TCPObjectSocket client) {
                boolean running = true;
                try {
                    while (running) // 保持在打开的连接上
                    {
                        SentMessage msg = (SentMessage) client.readObject();
                        /*
                         * 我们要么停止服务器，要么只是关闭此连接。
                         * （具体是哪种情况取决于服务器终止标志）
                         */
                        if (null == msg) {
                            actorSystemConnectionManager.closeRemoteSocket(client.getSocket());
                            running = false;
                        }
                        else {
                            try {
                                /*
                                 * 路径格式为 /system/...
                                 */
                                String path = new URI(msg.remotePath).getPath().substring(systemLength);
                                ActorRef sender = (null != msg.sender) ? msg.sender.remotify() : null;
                                ActorRef target = context.actorSelection(path);

                                // log.info("ActorSystem收到：{}，来自{}，要发送给{}", msg.getMessage(), sender, target);
                                if (null == target) {
                                    target = context.getDeadLetterActor();
                                    target.setIsDeadLetter(true);
                                }
                                if (null != sender) {
                                    sender = context.actorSelection(sender.path);
                                }
                                target.tell(msg.message, sender);
                                PrintWriter writer = new PrintWriter(client.getSocket().getOutputStream(), true);
                                writer.println("ACK"); // 应用级确认，用于序列化发送给同一Actor的消息
                                writer.flush();

                            } catch (Exception e) {
                                log.error("服务器错误：{}", e.getMessage());
                                e.printStackTrace();
                            }
                        }
                    }
                } catch (Exception e) {
                    // log.error("外层服务器错误：{}", e.getMessage());
                }
            }
        });
        return haveStopped;
    }

    /**
     * 获取到远程路径ActorSystem的连接
     *
     * @param uri 远程系统的URI
     * @return 包装的连接
     * @throws IOException IO问题时
     * @throws InterruptedException 被中断时
     */
    public WrappedTCPObjectSocket getSocket(URI uri) throws IOException, InterruptedException {
        return actorSystemConnectionManager.getSocket(uri);
    }

    /**
     * 将包装的套接字返回到连接池
     *
     * @param objectSocket 要返回到池的套接字
     */
    public void returnSocket(WrappedTCPObjectSocket objectSocket) {
        actorSystemConnectionManager.returnSocket(objectSocket);
    }

    /**
     * 出现了问题：尝试关闭到URI定义的Actor系统的所有连接
     *
     * @param uri 要清理连接的远程系统URI
     * @throws Exception 清理过程中的异常
     */
    public void flushPool(URI uri) throws Exception {
        actorSystemConnectionManager.flushPool(uri);
    }

    /**
     * Guardian包装类：封装Guardian Actor的引用和实例
     */
    private static class Guardian {
        /**
         * Guardian Actor的引用
         */
        final ActorRef ref;

        /**
         * Guardian Actor的实例
         */
        final GuardianActor actor;

        /**
         * 构造函数：创建Guardian包装对象
         *
         * @param ref Guardian Actor引用
         * @param actor Guardian Actor实例
         */
        public Guardian(ActorRef ref, GuardianActor actor) {
            this.ref = ref;
            this.actor = actor;
        }
    }
}
