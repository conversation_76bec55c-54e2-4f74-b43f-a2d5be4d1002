/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.PoisonPill;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.msgs.PingMsg;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * Ping Actor：用于默认Ping处理器，向发送者回复PingMsg
 *
 * 如果配置了非null参数，则将其视为要响应的最大消息数，
 * 然后告诉发送者死亡并自己也死亡。
 *
 * 这是一个用于网络连通性测试和性能测试的Actor，支持：
 * 1. 无限ping模式：持续响应ping消息
 * 2. 有限ping模式：响应指定次数后自动停止
 * 3. 性能统计：记录处理时间和消息数量
 *
 * 使用场景：
 * - 连通性测试：测试Actor之间的网络连接
 * - 性能基准：测量消息处理性能
 * - 负载测试：生成可控的消息负载
 * - 延迟测试：测量消息往返时间
 *
 * <AUTHOR>
 */
@Slf4j
public class PingActor extends Actor {

    /**
     * 剩余消息数：还能处理的消息数量
     */
    protected Integer remainingMessages;

    /**
     * 初始最大消息数：配置的最大消息处理数量
     */
    protected final Integer startMaxMessage;

    /**
     * 启动时间：用于计算总处理时间
     */
    final Long started;

    /**
     * 创建Props配置对象
     *
     * @param maxMessage 要发送的最大消息数
     * @return Props配置对象
     */
    public static Props props(Integer maxMessage) {
        return Props.create(PingActor.class, maxMessage);
    }

    /**
     * 构造函数：创建PingActor实例
     *
     * @param maxMessage 如果不为null，则为停止前的最大消息数
     */
    public PingActor(Integer maxMessage) {
        startMaxMessage = remainingMessages = maxMessage;
        started = System.currentTimeMillis();
    }

    /**
     * 停止后处理：记录性能统计信息
     */
    @Override
    public void postStop() {
        log.info("{}在{}毫秒内完成。发送了：{}条消息", self.path, (System.currentTimeMillis() - started), startMaxMessage);
    }

    /**
     * 默认行为
     * @return {@link ActorBehavior}
     */
    @Override
    protected ActorBehavior createBehavior() {

        return message -> {

            if (Objects.requireNonNull(message) instanceof PingMsg msg) {
                if (null != remainingMessages) {
                    if (0 == remainingMessages--) {
                        sender.tell(new PoisonPill(), null);
                        stopSelf();
                        return;
                    }
                }
                if (null != sender)
                    sender.tell(msg, self);
            } else {
                log.warn(self.path + " unexpected message: " + message);
            }
        };
    }

}