/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import lombok.Getter;

/**
 * 监督策略：定义当子Actor发生错误时的处理方式
 *
 * 可根据需要扩展。支持的策略包括：
 *
 * 策略类型：
 * - Stop（停止）：Actor被停止，不再处理消息。然后调用postStop()，其子Actor被递归停止。
 * - Resume（恢复）：调用onResume()，然后继续处理导致错误的消息之后的下一个可用消息。
 * - Restart（重启）：Actor被停止，但不调用postStop()。其子Actor被递归停止。然后其父Actor
 *   使用相同的名称（如果是随机生成的）和最初使用的相同props重新创建它。任何引用该Actor的
 *   ActorRef仍然有效。当Actor初始化时，将调用preRestart(Throwable t)而不是preStart()。
 *   t是导致重启的错误。
 *
 * 应用模式：
 * - MODE_ONE_FOR_ONE（一对一）：策略仅应用于失败的Actor
 * - MODE_ALL_FOR_ONE（一对全部）：策略应用于失败的Actor及其所有兄弟Actor
 *
 * 这种设计提供了灵活的错误处理机制，支持不同级别的故障隔离和恢复策略。
 *
 * <AUTHOR>
 */
@Getter
public class SupervisionStrategy {

    /** 错误时停止子Actor */
    public final static int SS_STOP = ActorRef.LC_STOP;
    /** 错误时恢复子Actor */
    public final static int SS_RESUME = ActorRef.LC_RESUME;
    /** 错误时重启子Actor */
    public final static int SS_RESTART = ActorRef.LC_RESTART;

    /** 策略仅应用于导致错误的子Actor */
    public final static int MODE_ONE_FOR_ONE = 0;
    /** 策略应用于导致错误的子Actor及其所有兄弟Actor */
    public final static int MODE_ALL_FOR_ONE = 1;

    /**
     * 应用模式：定义策略的影响范围
     */
    final int mode;

    /**
     * 策略类型：定义具体的错误处理方式
     */
    final int strategy;

    /**
     * 默认策略：对任何错误都停止出错的子Actor
     */
    public SupervisionStrategy() {
        strategy = SS_STOP;
        mode = MODE_ONE_FOR_ONE;
    }

    /**
     * 自定义策略构造函数
     *
     * @param strategy 要使用的策略类型
     * @param mode 要使用的应用模式
     */
    public SupervisionStrategy(int strategy, int mode) {
        this.strategy = strategy;
        this.mode = mode;
    }

    /**
     * 策略决策方法：可被重写以返回依赖于子Actor和错误的策略
     *
     * 默认实现返回当前策略，适用于所有情况。
     * 子类可以重写此方法以实现更复杂的策略决策逻辑。
     *
     * @param ref 应用策略的子Actor
     * @param thrown 应用策略的错误
     * @return 自定义策略
     */
    protected SupervisionStrategy strategy(ActorRef ref, Throwable thrown) {
        return this;
    }

    /**
     * 用于日志记录的便利字符串表示
     *
     * @return 策略的字符串描述
     */
    @Override
    public String toString()
    {
        String SMode = mode == MODE_ONE_FOR_ONE ? "一对一" : "一对全部";
        String SStrategy = strategy == SS_RESTART ? "重启" : strategy == SS_STOP ? "停止" : "恢复";

        return String.format("监督者：模式=%s，策略=%s", SMode, SStrategy);
    }
}

