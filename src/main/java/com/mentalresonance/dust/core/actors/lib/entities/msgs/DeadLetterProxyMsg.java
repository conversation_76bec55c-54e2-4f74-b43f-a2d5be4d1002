/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib.entities.msgs;

import java.io.Serializable;
import com.mentalresonance.dust.core.actors.ActorRef;

/**
 * 由我们的PodDeadLetterActor发送。名称处的子Actor不存在，所以创建它并将消息传递给它。
 *
 * <AUTHOR>
 */
public class DeadLetterProxyMsg implements Serializable {
    /**
     * 要创建的子级名称
     */
    public final String name;
    /**
     * 要发送的消息
     */
    public final Serializable msg;
    /**
     * 原始消息的发送者
     */
    public final ActorRef sender;

    /**
     * 构造函数
     * @param name 要创建的子级名称
     * @param msg 要发送给子级的消息
     * @param sender 原始消息的发送者
     */
    public  DeadLetterProxyMsg(String name, Serializable msg, ActorRef sender) {
        this.name = name; this.msg = msg; this.sender = sender;
    }
}