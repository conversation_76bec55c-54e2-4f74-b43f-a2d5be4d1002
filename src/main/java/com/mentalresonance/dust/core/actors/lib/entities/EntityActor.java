/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib.entities;

import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.PersistentActor;
import com.mentalresonance.dust.core.actors.lib.entities.msgs.EntityStateMsg;
import com.mentalresonance.dust.core.msgs.SnapshotMsg;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Objects;

/**
 * 实体Actor：对应现实世界实体（汽车、人员、关系等）的Actor，因此具有状态
 *
 * 实体Actor具有唯一的名称，总是在该名称下创建，但可能在不同路径上存在多个实例
 * （因此具有不同的状态）。
 *
 * 实体Actor的特点：
 * 1. 状态管理：维护类型化的状态对象
 * 2. 持久化：继承自PersistentActor，支持状态持久化
 * 3. 多实例：同一实体可在不同路径下有多个实例
 * 4. 生命周期管理：智能的状态保存和删除策略
 *
 * 使用场景：
 * 1. 业务实体：用户、订单、产品等业务对象
 * 2. 游戏对象：角色、物品、场景等游戏实体
 * 3. 设备管理：IoT设备的状态管理
 * 4. 会话管理：用户会话和连接状态
 * 5. 缓存实体：需要持久化的缓存对象
 *
 * 状态管理策略：
 * - 正常停止：删除快照（生命周期结束）
 * - 系统关闭：保存快照（确保状态不丢失）
 *
 * @param <T> 状态类型，必须可序列化
 * <AUTHOR>
 */
@Slf4j
public abstract class EntityActor<T extends Serializable> extends PersistentActor {

    /**
     * 定义实体的状态：实体的核心业务数据
     */
    protected T state;

    /**
     * 默认构造函数：创建实体Actor
     */
    public EntityActor() {}

    /**
     * 通常如果一个EntityActor正在停止，这意味着它已到达生命周期的终点，
     * 因此应该删除其快照。但如果我们在关闭系统，我们希望做相反的事情并确保我们的状态被保存。
     */
    @Override
    protected void postStop() {
        if (isInShutdown()) {
            saveSnapshot(state);
        }
        else {
            deleteSnapshot();
        }
    }
    /**
     * 待重写。提供名称的初始状态到要发送给自己的EntityStateMsg中。
     * 通常这只做一次 - 之后快照就足够了。
     * @param name 实体的名称
     */
    protected void getState(String name) {
       self.tell(new EntityStateMsg<T>(null), self);
    }

    /**
     * 待重写。在我们收到EntityStateMsg并切换到createBehaviour后调用
     * @param name 实体的名称
     */
    protected void postGetState(String name) {}

    protected ActorBehavior recoveryBehavior() {
        return message -> {
            if (Objects.requireNonNull(message) instanceof SnapshotMsg msg) {
                state = (T) msg.getSnapshot();
                if (null == state) {
                    become(waitForStateBehavior());
                    getState(self.name);
                } else {
                    become(createBehavior());
                    unstashAll();
                }
            } else {
                stash(message);
            }
        };
    }

    /**
     * 设置行为等待接收状态
     * @return {@link ActorBehavior}
     */
    protected ActorBehavior waitForStateBehavior() {
        return message -> {
            if (Objects.requireNonNull(message) instanceof EntityStateMsg msg) {
                state = (T) msg.getState();
                become(createBehavior());
                postGetState(self.name);
                unstashAll();
            } else {
                stash(message);
            }

        };
    }
}