/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.msgs.ChildExceptionMsg;
import com.mentalresonance.dust.core.msgs.Terminated;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.LinkedList;

/**
 * 维护一个（相同）服务Actor的虚拟池。当槽位打开时，将传入的消息交给这些Actor处理。
 *
 * 这些服务Actor在处理完一个任务后<b>必须</b>死亡
 *
 *  <AUTHOR>
 */
@Slf4j
public class ServiceManagerActor extends Actor {

    final Props serviceProps;
    final int maxWorkers;
    int currentWorkers;

    // [msg, sender]
    final LinkedList<LinkedList<Serializable>> msgQ = new LinkedList<>();

    /**
     * 创建Actor props
     * @param serviceProps 服务Actor的props
     * @param maxWorkers 可用的最大槽位数
     * @return Props
     */
    public static Props props(Props serviceProps, Integer maxWorkers) {
        return Props.create(ServiceManagerActor.class, serviceProps, maxWorkers);
    }

    /**
     * 构造函数
     * @param serviceProps 服务Actor的props
     * @param maxWorkers 可用的最大槽位数
     */
    public ServiceManagerActor(Props serviceProps, Integer maxWorkers) {
        this.serviceProps = serviceProps;
        this.maxWorkers = maxWorkers;
        currentWorkers = 0;
    }

    @Override
    protected void preStart() throws Exception {
        super.preStart();
        self.tell(new _ServiceManagerPumpMsg(), self);
    }

    /**
     * 默认行为
     * @return {@link ActorBehavior}
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch(message) {
                case _ServiceManagerPumpMsg ignored -> {
                    if (!msgQ.isEmpty() && currentWorkers < maxWorkers) {
                        LinkedList<Serializable> record = msgQ.removeFirst();
                        watch(actorOf(serviceProps)).tell(record.getFirst(), (ActorRef) record.getLast());
                        ++currentWorkers;
                        log.trace("服务管理器泵送 {} 有 {} 个工作线程。最大值={}。邮箱大小={}", self.path, currentWorkers, maxWorkers, self.mailBox.getQueue().size());
                    }
                }

                case Terminated ignored -> {
                    --currentWorkers;
                    log.trace("服务管理器终止 {} 现在有 {} 个工作线程。最大值={}。邮箱大小={}", self.path, currentWorkers, maxWorkers, self.mailBox.getQueue().size());
                    self.tell(new _ServiceManagerPumpMsg(), self);
                }

                case ChildExceptionMsg ignored -> {} // 否则默认会为其创建一个子级

                default -> {
                    if (currentWorkers < maxWorkers) {
                        watch(actorOf(serviceProps)).tell(message, sender);
                        ++currentWorkers;
                        log.trace("服务管理器提交 {} 有 {} 个工作线程。最大值={}。邮箱大小={}", self.path, currentWorkers, maxWorkers, self.mailBox.getQueue().size());
                    }
                    else {
                        // 不能使用List.of()，因为它不允许空条目，而我们可能有空的发送者
                        LinkedList<Serializable> tuple = new LinkedList<>();
                        tuple.add(message);
                        tuple.add(sender);
                        msgQ.add(tuple);
                        log.trace("服务管理器添加 {} 有 {} 个工作线程。最大值={}。邮箱大小={}", self.path, currentWorkers, maxWorkers, self.mailBox.getQueue().size());
                    }
                }
            }
        };
    }

    // 不要使用StartMsg()，因为我们可能正在向服务工作者发送该消息！
    static class _ServiceManagerPumpMsg implements Serializable {}
}