/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import java.io.Serializable;

/**
 * Actor行为接口：便于实现简单的父类消息处理
 *
 * 这个接口定义了Actor处理消息的标准方法，提供了：
 * 1. 统一的消息处理接口
 * 2. 异常传播机制
 * 3. 行为的可组合性
 * 4. 支持动态行为切换
 *
 * 与函数式接口不同，此接口允许抛出异常，
 * 使得消息处理过程中的错误能够被适当地传播和处理。
 *
 * 使用示例：
 * ```java
 * public class MyActor extends Actor {
 *     @Override
 *     protected ActorBehavior createBehavior() {
 *         return message -> {
 *             switch(message) {
 *                 case StartMsg msg -> handleStart(msg);
 *                 case StopMsg msg -> handleStop(msg);
 *                 default -> log.warn("未处理的消息：{}", message);
 *             }
 *         };
 *     }
 * }
 * ```
 *
 * <AUTHOR>
 */
public interface ActorBehavior {
    /**
     * 调用此方法来处理消息
     *
     * 实现此方法来定义Actor对特定消息的响应逻辑。
     * 方法应该：
     * 1. 快速处理消息，避免长时间阻塞
     * 2. 处理所有可能的消息类型
     * 3. 对未知消息类型提供适当的默认处理
     * 4. 允许异常向上传播以触发监督策略
     *
     * @param message 要处理的消息
     * @throws Exception 消息处理过程中的任何异常
     */
    void onMessage(Serializable message) throws Exception;
}


