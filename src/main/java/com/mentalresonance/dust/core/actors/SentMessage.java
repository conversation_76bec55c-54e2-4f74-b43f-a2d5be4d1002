/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors;

import java.io.Serializable;

/**
 * 已发送消息：任意消息及其发送者的包装
 *
 * 这个数据包是向Actor传递消息的传输机制。
 *
 * 作为消息传递系统的核心数据结构，用于：
 * 1. 封装消息内容和发送者信息
 * 2. 支持本地和远程消息传递
 * 3. 提供消息路由所需的元数据
 * 4. 支持消息的序列化传输
 *
 * 设计特点：
 * - 不可变性：消息和发送者字段为final
 * - 轻量级：只包含必要的传输信息
 * - 可序列化：支持网络传输
 * - 路径支持：支持远程Actor的路径信息
 *
 * <AUTHOR>
 */
public class SentMessage implements Serializable {
    /**
     * 要投递的消息：实际的业务消息内容
     */
    public final Serializable message;

    /**
     * 目标路径：如果是远程消息则为目标路径，否则为null
     * 用于跨网络的Actor通信路由
     */
    public String remotePath = null;

    /**
     * 消息发送者：发送此消息的Actor引用
     * 用于回复消息和错误处理
     */
    public final ActorRef sender;

    /**
     * 构造函数：创建已发送消息实例
     *
     * @param message 正在发送的消息
     * @param sender 声称正在发送消息的Actor
     */
    public SentMessage(Serializable message, ActorRef sender) {
        this.message = message;
        this.sender = sender;
    }
}
