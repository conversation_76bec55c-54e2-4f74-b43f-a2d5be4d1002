/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib.entities;

import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.PersistentActor;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.actors.lib.entities.msgs.DeadLetterProxyMsg;
import com.mentalresonance.dust.core.actors.lib.entities.msgs.RegisterPodDeadLettersMsg;
import com.mentalresonance.dust.core.msgs.*;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Objects;

/**
 * 管理相同子级的Pod。响应CreateChildMSg通过创建命名的子级并可能传递消息来处理。
 * 这是持久化的，因此它将创建它所知道的所有子级，但由这些子级来恢复它们想要依赖的任何状态。
 *
 *  <AUTHOR>
 */
@Slf4j
public class PodManagerActor extends PersistentActor {
    /**
     * 子级的通用Props
     */
    protected final Props childProps;
    /**
     * 子级映射 - 允许在恢复时重建
     */
    public HashMap<String, Boolean> kids;

    /**
     * 通常如果我们按需创建了一个子级，我们会向发送者回复一个CreatedChildMsg。
     * 如果子级是一个服务Actor（比如正在完成中），并且想法是服务Actor直接响应发送者，这就会中断。
     * 现在服务Actor回复和PodManager回复之间存在竞争条件。如果这是一个完成操作，先完成的会获胜，
     * 因此Completion的另一端可能会收到CreatedChildMsg而不是他们期望的消息。
     *
     * 默认是发送CreatedChildMsg，但此标志使我们能够关闭该行为。
     */
    protected boolean notifyCreatedChild;

    /**
     * 指向PodDeadLetterActor的引用
     */
    protected final ActorRef podDeadLetterRef;

    /**
     * 默认PodManager创建 - 子级将由childProps确定
     * @param childProps 定义要创建的子级的props
     * @return {@link Props}
     */
    public static Props props(Props childProps) {
        return Props.create(PodManagerActor.class, childProps, null, true);
    }

    /**
     * 创建PodManager但注册到预先存在的PodDeadLetterActor，这样我可以按需自动创建子级（有消息发送给它们）。
     * @param childProps 定义要创建的子级的props
     * @param podDeadLetterRef 指向{@link PodDeadLetterActor}的引用
     * @return {@link Props}
     */
    public static Props props(Props childProps, ActorRef podDeadLetterRef) {
        return Props.create(PodManagerActor.class, childProps, podDeadLetterRef, true);
    }

    /**
     * 创建PodManager但注册到预先存在的PodDeadLetterActor，这样我可以按需自动创建子级（有消息发送给它们）。
     * @param childProps 定义要创建的子级的props
     * @param podDeadLetterRef 指向{@link PodDeadLetterActor}的引用
     * @return {@link Props}
     */
    public static Props props(Props childProps, ActorRef podDeadLetterRef, boolean notifyCreatedChild) {
        return Props.create(PodManagerActor.class, childProps, podDeadLetterRef, notifyCreatedChild);
    }

    /**
     * 构造函数
     * @param childProps 定义要创建的子级的props
     * @param podDeadLetterRef 指向{@link PodDeadLetterActor}的引用
     * @param notifyCreatedChild 如果为true，则通知创建了子级的消息的发送者CreatedChildMsg，否则不通知
     */
    public PodManagerActor(Props childProps, ActorRef podDeadLetterRef, boolean notifyCreatedChild) {
        this.childProps = childProps;
        this.podDeadLetterRef = podDeadLetterRef;
        this.notifyCreatedChild = notifyCreatedChild;
    }

    /**
     * 构造函数
     * @param childProps 定义要创建的子级的props
     * @param podDeadLetterRef 指向{@link PodDeadLetterActor}的引用
     */
    public PodManagerActor(Props childProps, ActorRef podDeadLetterRef) {
        this.childProps = childProps;
        this.podDeadLetterRef = podDeadLetterRef;
        this.notifyCreatedChild = true;
    }

    @Override
    public void preStart() throws Exception {
        super.preStart();
        log.trace("已启动 {}。podDeadLetterRef={}", self.path, podDeadLetterRef);
        if (null != podDeadLetterRef) {
            podDeadLetterRef.tell(new RegisterPodDeadLettersMsg(), self);
        }
    }

    @Override
    public Class getSnapshotClass() { return HashMap.class; }

    /**
     * 在启动时恢复子级
     * @return ActorBehavior
     */
    @Override
    protected ActorBehavior recoveryBehavior() {
        return message -> {
            if (Objects.requireNonNull(message) instanceof SnapshotMsg msg) {
                kids = (HashMap<String, Boolean>) msg.getSnapshot();
                if (null == kids) kids = new HashMap<>();
                for (String name : kids.keySet()) {
                    watch(actorOf(childProps, name));
                    kids.put(name, true);
                }
                become(createBehavior());
                unstashAll();
            } else {
                stash(message);
            }
        };
    }

    /**
     * 默认行为
     * @return {@link ActorBehavior}
     */
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch(message) {
                case Terminated ignored -> {
                    kids.remove(sender.name);
                    saveSnapshot(kids);
                }
                case CreateChildMsg msg -> {
                    String name = msg.getName();
                    if (kids.containsKey(name)) {
                        log.warn("{}: CreateChildMsg 来自 {}。子级 '{}' 已存在。", self.path, sender, name);
                    }
                    else {
                        ActorRef child = actorOf(childProps, name);
                        watch(child);
                        kids.put(name, true);
                        if (null != msg.getMsg()) {
                            child.tell(msg.getMsg(), sender);
                        }
                        saveSnapshot(kids);
                        if (notifyCreatedChild && null != sender) {
                            sender.tell(new CreatedChildMsg(name), self);
                        }
                        log.trace("{} 创建了子级: {}", self.path, name);
                    }
                }
                case ChildExceptionMsg msg -> {
                    log.error("{}: 子级异常 {}", self.path, msg.getException().getMessage());
                    unWatch(sender);
                    kids.remove(sender.name);
                }
                case DeadLetterProxyMsg msg -> self.tell(new CreateChildMsg(msg.name, msg.msg), msg.sender);

                // 子级创建的确认 - 忽略
                case CreatedChildMsg ignored -> {}

                // 在PodDeadLetterActor注册的确认 - 忽略
                case RegisterPodDeadLettersMsg ignored -> {}

                default -> super.createBehavior().onMessage(message);
            }
        };
    }
}
