/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.msgs.PubSubMsg;
import com.mentalresonance.dust.core.msgs.Terminated;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.HashMap;
import java.util.LinkedList;


/**
 * 发布订阅Actor：其他Actor可以订阅此Actor以接收转发的消息
 *
 * PubSubActor实现了发布-订阅模式，允许Actor之间进行解耦的消息通信。
 *
 * 主要功能：
 * 1. 消息订阅：Actor通过指定感兴趣的消息类来注册订阅
 * 2. 消息发布：当收到已订阅类型的消息时，转发给所有订阅者
 * 3. 订阅管理：支持订阅和取消订阅操作
 * 4. 自动清理：当订阅者终止时自动清理订阅关系
 *
 * 使用方式：
 * - 订阅：发送{@link PubSubMsg}消息进行订阅/取消订阅
 * - 发布：直接向PubSubActor发送消息，会转发给相应的订阅者
 *
 * 重要提示：
 * <b>发送给订阅者的消息不会被克隆 - 接收者需要假设其他客户端也可能拥有相同的消息，
 * 因此需要相应地处理共享状态问题</b>
 *
 * <AUTHOR>
 */
@Slf4j
public class PubSubActor extends Actor {

    /**
     * 默认构造函数：创建PubSubActor实例
     */
    public PubSubActor() {}

    /**
     * 创建PubSubActor的Props配置对象
     *
     * @return Props配置对象
     */
    public static Props props() {
        return Props.create(PubSubActor.class);
    }

    /**
     * 订阅映射表：消息类的完全限定名 -> 该类的注册者映射表
     *
     * 结构：消息类名 -> (Actor路径 -> ActorRef)
     * 这种两级映射结构便于快速查找和管理订阅关系
     */
    final protected HashMap<String, HashMap<String, ActorRef>> subs = new HashMap<String, HashMap<String, ActorRef>>();

    /**
     * 创建默认行为：处理发布订阅相关的消息
     *
     * 消息处理逻辑：
     * 1. {@link PubSubMsg}：通过更新订阅映射表来处理订阅/取消订阅请求
     * 2. 普通消息：如果消息类型有订阅者，发送内部消息(_Publish)进行分发
     * 3. _Publish：从订阅者列表中取出一个订阅者发送消息，然后继续处理剩余订阅者
     * 4. Terminated：清理已终止Actor的订阅关系
     *
     * @return 消息处理行为
     */
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                case PubSubMsg msg -> {
                    // 获取指定消息类型的订阅者列表
                    HashMap<String, ActorRef> registrants = subs.get(msg.fqn);

                    log.trace("{}：收到来自{}对{}的订阅请求", self.path, sender.path, msg.fqn);

                    if (msg.subscribe) {
                        // 处理订阅请求
                        if (null == registrants) {
                            // 首次订阅此消息类型
                            registrants = new HashMap<>();
                            registrants.put(sender.path, sender);
                            subs.put(msg.fqn, registrants);
                        } else {
                            // 添加到现有订阅者列表
                            registrants.computeIfAbsent(sender.path, k -> sender);
                        }

                        // 监视订阅者，以便在其终止时清理订阅关系
                        watch(sender);
                    }
                    else {
                        // 处理取消订阅请求
                        if (null != registrants) {
                            registrants.remove(sender.path);
                            // 如果该消息类型没有订阅者了，移除整个映射
                            if (registrants.isEmpty()) {
                                subs.remove(msg.fqn);
                                // 如果没有任何订阅了，发送内部通知
                                if (subs.isEmpty())
                                    tellSelf(new _NoSubscriptions());
                            }
                        }
                    }
                }
                case _Publish msg -> {
                    // 处理内部发布消息：逐个向订阅者发送消息
                    if (!msg.subs.isEmpty()) {
                        // 取出第一个订阅者并发送消息
                        msg.subs.removeFirst().tell(msg.msg, msg.sender);
                        // 继续处理剩余的订阅者
                        self.tell(msg, self);
                    }
                }
                case Terminated terminated -> {
                    // 处理订阅者终止：清理相关的订阅关系
                    log.trace("{}：收到来自{}的终止消息", self.path, sender.path);
                    LinkedList<String> toRemove = new LinkedList<>();
                    for (String fqn : subs.keySet()) {
                        HashMap<String, ActorRef> registrants = subs.get(fqn);
                        registrants.remove(sender.path);
                        if (registrants.isEmpty())
                            toRemove.add(fqn);
                    }
                    for (String fqn : toRemove) {
                        subs.remove(fqn);
                    }
                    if (subs.isEmpty())
                        tellSelf(new _NoSubscriptions());
                }
                case _NoSubscriptions ignored -> {
                    // 默认行为是停止。如果需要其他行为，请在子类中重写。
                    log.trace("{} 没有订阅者。正在停止...", self.path);
                    stopSelf();
                }
                case null, default -> {
                    HashMap<String, ActorRef> registrants = subs.get(message.getClass().getName());
                    if (null != registrants) {
                        _Publish publish = new _Publish(sender, message, new LinkedList<>(registrants.values().stream().toList()));
                        self.tell(publish, self);
                    }
                }
            }
        };
    }

    /**
     * 用于分发订阅消息的内部消息
     */
    static protected class _Publish implements Serializable {
        /**
         * 订阅消息的发送者
         */
        @Getter
        final
        ActorRef sender;

        /**
         * 订阅的消息 - 我们允许这个消息被覆盖，例如当我们希望向每个订阅者发布此消息的克隆时
         */
        @Getter
        @Setter
        Serializable msg;
        /**
         * 订阅者
         */
        @Getter
        final
        LinkedList<ActorRef> subs;

        /**
         * 构造函数
         * @param sender 事件的发送者
         * @param msg 事件
         * @param subs 要接收消息的订阅者列表
         */
        public _Publish(ActorRef sender, Serializable msg, LinkedList<ActorRef> subs) {
            this.sender = sender;
            this.msg = msg;
            this.subs = subs;
        }
    }

    static protected class _NoSubscriptions implements Serializable {}
}