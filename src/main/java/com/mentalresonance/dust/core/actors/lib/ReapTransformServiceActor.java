package com.mentalresonance.dust.core.actors.lib;

import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.Props;
import com.mentalresonance.dust.core.msgs.GetChildrenMsg;
import com.mentalresonance.dust.core.msgs.StartMsg;

import java.io.Serializable;
import java.util.List;
import java.util.function.Function;

import static com.mentalresonance.dust.core.actors.lib.ReaperActor.ReapMsg.ReapResponseMsg;

/**
 * 一种常见模式。向所有父级的子级发送收割消息，转换结果
 * 并将其作为来自我父级的消息发送给客户端，然后停止。非常有用，例如获取
 * PodManager子级的状态并准备该状态以进行进一步传递
 */
public class ReapTransformServiceActor extends Actor {

    ActorRef host, client;
    Class<? extends Serializable> reapingClz;
    Function<Object, Serializable> transform;

    /**
     * @param host - 子级的父级，我们将向其发送收割消息
     * @param client - 请求者 - 将获得结果
     * @param reapingClz - 要发送给子级的消息类。应该有一个默认的无参构造函数
     * @param transform - { ReapResponseMsg -> ... } 值被发送给客户端
     * @return Props
     */
    public static Props props(ActorRef host, ActorRef client,
                                Class<? extends Serializable> reapingClz, Function<Object, Serializable> transform) {
        return Props.create(ReapTransformServiceActor.class, host, client, reapingClz, transform);
    }

    public ReapTransformServiceActor(ActorRef host, ActorRef client,
                                 Class<? extends Serializable> reapingClz, Function<Object, Serializable> transform) {
        this.host = host;
        this.client = client;
        this.reapingClz = reapingClz;
        this.transform = transform;
    }

    @Override
    protected ActorBehavior createBehavior() {
        return (Serializable message) -> {
            switch(message) {
                case StartMsg ignored:
                    host.tell(new GetChildrenMsg(), self);
                    break;

                /*
                 警告。常见用法模式是在主机本身上调用此Actor。但这意味着self现在是
                 host的子级，这将导致尝试收割自己....所以过滤掉我。
                 */
                case GetChildrenMsg msg:
                    List<ActorRef> childs = msg.getChildren().stream().filter(child -> child != self).toList();
                    actorOf(ReaperActor.props(10000L)).tell(
                        new ReaperActor.ReapMsg(
                            reapingClz,
                            childs,
                            ReapResponseMsg.class
                        ),
                        self
                    );
                    break;

                case ReapResponseMsg msg:
                    client.tell(transform.apply(msg), parent);
                    stopSelf();
                    break;

                default: super.createBehavior().onMessage(message);
            }
        };
    }
}