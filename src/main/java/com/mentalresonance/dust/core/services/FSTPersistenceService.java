/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.services;

import com.mentalresonance.dust.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.Semaphore;

/**
 * FST持久化服务：使用FST序列化将Actor状态存储/检索到文件系统
 *
 * FST（Fast Serialization）是一个高性能的Java序列化库，具有以下特点：
 * 1. 性能优异：比标准Java序列化快数倍
 * 2. 兼容性好：支持大多数Java对象的序列化
 * 3. 体积小：序列化后的数据更紧凑
 * 4. 跨版本：对类结构变化有一定的容忍度
 *
 * 适用场景：
 * - 对性能要求较高的生产环境
 * - 需要频繁序列化/反序列化的场景
 * - 对存储空间有要求的应用
 *
 * <AUTHOR>
 */
@Slf4j
public class FSTPersistenceService implements PersistenceService {

    /**
     * 最大并发数：限制同时进行的持久化操作数量
     */
    private static final int MAX_CONCURRENT = 16;

    /**
     * 信号量：控制并发访问的信号量
     */
    static final Semaphore available = new Semaphore(MAX_CONCURRENT, true);

    /**
     * 单例实例：确保全局只有一个持久化服务实例
     */
    private static FSTPersistenceService fstPersistenceService = null;

    /**
     * 用户主目录：系统属性获取的用户主目录路径
     */
    private static final String HOME = System.getProperty("user.home");

    /**
     * 快照存储路径：快照文件在文件系统中的存储位置
     */
    public static Path SNAPSHOTS;

    /**
     * 私有构造函数：防止外部直接实例化
     */
    private FSTPersistenceService() {}

    /**
     * 工厂方法：创建持久化服务实例（使用默认路径）
     *
     * 确定快照的存储位置为用户主目录下的snapshots文件夹
     *
     * @throws IOException 文件系统IO错误时
     * @return 持久化服务实例
     */
    public static PersistenceService create() throws IOException {
        return create(HOME + "/snapshots");
    }

    /**
     * 工厂方法：创建持久化服务实例（指定路径）
     *
     * @param path 快照存储路径
     * @throws IOException 文件系统IO错误时
     * @return 持久化服务实例
     */
    public static PersistenceService create(String path) throws IOException {
        if (null == fstPersistenceService) {
            Path p = Paths.get(path);
            // 如果目录不存在则创建
            if (Files.notExists(p)) {
                Files.createDirectory(p);
            }
            SNAPSHOTS = p;
            fstPersistenceService = new FSTPersistenceService();
            log.info("FST快照存储位置：{}", p.toString());
        }
        return fstPersistenceService;
    }

    /**
     * 对ID进行哈希处理：使用MD5算法对ID进行哈希
     *
     * @param id 要哈希的ID
     * @return 哈希后的ID字符串
     * @throws NoSuchAlgorithmException 哈希算法不存在时
     */
    private String hashId(String id) throws NoSuchAlgorithmException {
        return StringUtils.hash(id, "MD5");
    }

    /**
     * 将对象写入指定ID下的文件
     *
     * 文件名格式为："md5(id).snap"
     * 使用FST序列化确保高性能的数据存储。
     *
     * @param id 执行写入操作的对象标识符
     * @param object 要保存的数据
     * @throws InterruptedException 被中断时
     * @throws NoSuchAlgorithmException 哈希算法错误时
     * @throws IOException 发生IO错误时
     */
    public void write(String id, Serializable object) throws InterruptedException, NoSuchAlgorithmException, IOException
    {
        // 获取信号量，控制并发访问
        available.acquire();

        try {
            // 使用FST序列化对象
            byte[] blob = SerializationService.write(object);
            Path p = Paths.get(SNAPSHOTS + "/" + hashId(id) + ".snap");
            // 写入文件（创建或覆盖现有文件）
            Files.write(p, blob, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            log.debug("成功写入快照：{}", id);
        }
        catch (Throwable e) {
            log.error("写入快照失败：{}", e.getMessage());
            e.printStackTrace();
            throw e;
        }
        finally {
            available.release();
        }
    }

    /**
     * 读取指定ID的对象
     *
     * @param id 对象的标识符
     * @return 如果序列化文件存在则返回反序列化的对象，否则返回null
     * @throws InterruptedException 读取时被中断
     * @throws IOException 文件系统错误
     * @throws ClassNotFoundException 无法找到反序列化的类
     * @throws NoSuchAlgorithmException 哈希算法错误
     */
    public Serializable read(String id) throws InterruptedException, IOException, ClassNotFoundException, NoSuchAlgorithmException
    {
        Serializable object;

        // 获取信号量，控制并发访问
        available.acquire();

        try {
            Path p = Paths.get(SNAPSHOTS + "/" + hashId(id) + ".snap");
            if (! Files.exists(p)) {
                object = null;
                log.debug("快照文件不存在：{}", id);
            } else {
                // 使用FST反序列化对象
                object = SerializationService.read(Files.readAllBytes(p));
                log.debug("成功读取快照：{}", id);
            }
        }
        catch (Throwable e) {
            log.error("读取快照失败：{}", e.getMessage());
            throw e;
        }
        finally {
            available.release();
        }
        return object;
    }

    @Override
    public Serializable read(String id, Class<?> clz) throws Exception {
        Serializable object;

        available.acquire();

        try {
            Path p = Paths.get(SNAPSHOTS + "/" + hashId(id) + ".snap");
            if (! Files.exists(p))
                object = null;
            else
                object = SerializationService.read(Files.readAllBytes(p), clz);
        }
        catch (Throwable e) {
            log.error(String.format("Read Snapshot: %s", e.getMessage()));
            throw e;
        }
        finally {
            available.release();
        }
        return object;
    }

    /**
     * Delete the object at id
     * @param id of the object
     * @throws InterruptedException if interrupted during delete
     */
    public void delete(String id) throws InterruptedException, NoSuchAlgorithmException, IOException {

        available.acquire();

        try {
            Path p = Paths.get(SNAPSHOTS + "/" + hashId(id) + ".snap");
            if (Files.exists(p))
                Files.delete(p);
        }
        catch (Exception e) {
            log.error("Delete Snapshot: %s".formatted(e.getMessage()));
            throw e;
        }
        finally {
            available.release();
        }
    }
}
