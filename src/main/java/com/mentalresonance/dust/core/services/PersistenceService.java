/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.services;

import java.io.Serializable;

/**
 * 持久化服务接口：Actor持久化使用此接口定义的统一持久化服务
 *
 * 我们提供了开箱即用的数据库和文件系统持久化服务。
 *
 * 此接口为不同的持久化实现提供统一的API，支持多种存储后端：
 * 1. 文件系统存储（FST、Jackson、Gson）
 * 2. 数据库存储（关系型、NoSQL）
 * 3. 内存存储（用于测试）
 * 4. 分布式存储（Redis、Hazelcast等）
 *
 * 设计原则：
 * - 简单易用：提供基本的CRUD操作
 * - 异常透明：将底层存储异常暴露给调用者
 * - 并发安全：实现类需要保证线程安全
 * - 类型支持：支持带类型信息的反序列化
 *
 * <AUTHOR>
 */
public interface PersistenceService {

    /**
     * 持久化对象：将对象保存到指定ID下
     *
     * @param id 用于定位对象的标识符
     * @param object 要持久化的对象
     * @throws Exception 发生错误时
     */
    void write(String id, Serializable object) throws Exception;

    /**
     * 删除指定ID的对象（如果存在）
     *
     * 这允许我们即使在快照尚未保存时也能干净地退出。
     * 用于清理临时状态和释放存储空间。
     *
     * @param id 要删除的状态的ID
     * @throws Exception 如果出现问题
     */
    void delete(String id) throws Exception;

    /**
     * 读取指定ID的对象
     *
     * @param id 对象ID
     * @return 反序列化的对象，如果不存在则返回null
     * @throws Exception 如果出现问题
     */
    Serializable read(String id) throws Exception;

    /**
     * 读取指定ID的对象（带类型信息）
     *
     * 某些序列化机制可能需要类型信息来正确反序列化对象。
     * 例如Gson需要明确的类型信息来处理泛型和复杂对象。
     *
     * @param id 对象ID
     * @param clz 返回对象应该是此类的实例
     * @return 反序列化的对象
     * @throws Exception 如果出现问题
     */
    Serializable read(String id, Class<?> clz) throws Exception;
}
