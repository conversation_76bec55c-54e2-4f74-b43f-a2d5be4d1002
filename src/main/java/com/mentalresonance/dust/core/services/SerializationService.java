/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.services;

import com.google.gson.Gson;
import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.SentMessage;
import com.mentalresonance.dust.core.msgs.PingMsg;
import lombok.Getter;
import org.nustaq.serialization.*;
import java.io.IOException;
import java.io.Serializable;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 序列化服务：使用FST（<a href="https://github.com/RuedigerMoeller/fast-serialization">
 * nustaq serializer</a>）进行对象的序列化和反序列化
 *
 * 该服务提供了高性能的对象序列化功能，支持：
 * 1. 二进制序列化：用于网络传输和持久化
 * 2. JSON序列化：用于调试和外部API
 * 3. 类注册优化：预注册类以提高序列化性能
 * 4. 多种序列化格式：支持FST和Gson
 *
 * <AUTHOR>
 */
public class SerializationService {

    /**
     * 私有构造函数：防止实例化，这是一个工具类
     */
    private SerializationService() {}

    /**
     * FST二进制配置：用于高性能的二进制序列化
     */
    @Getter
    static final FSTConfiguration fstConfiguration = FSTConfiguration.createUnsafeBinaryConfiguration();

    /**
     * FST JSON配置：用于JSON格式的序列化
     */
    static final FSTConfiguration fstJsonConfiguration = FSTConfiguration.createJsonConfiguration();

    // 静态初始化块：配置序列化器
    static {
        // 注册核心类以提高序列化性能
        fstConfiguration.registerClass(SentMessage.class, ActorRef.class, PingMsg.class);
        fstConfiguration.setShareReferences(false);  // 禁用引用共享以提高性能
        fstConfiguration.registerSerializer(LinkedHashMap.class, new FSTLinkedHasMapSerializer(), true);

        fstJsonConfiguration.registerClass(SentMessage.class, ActorRef.class, PingMsg.class);
        fstJsonConfiguration.setShareReferences(false);

        // 解决FST的已知问题
        // https://github.com/RuedigerMoeller/fast-serialization/issues/103
        // FSTUtil.unFlaggedUnsafe = null;
    }

    /**
     * 向序列化器注册类。注册的类可能转换更快，因为不会涉及反射。
     * 建议在应用启动时执行一次。
     *
     * @param classes 要注册的（可序列化）类列表
     */
    public static void registerClasses(Class<Serializable>[] classes) {
        for (Class<Serializable> clz: classes) {
            fstConfiguration.registerClass(clz);
            fstJsonConfiguration.registerClass(clz);
        }
    }

    /**
     * 序列化对象为字节数组
     *
     * @param object 要序列化的对象
     * @return 序列化后的字节数组
     */
    public static byte[] write(Serializable object) {
        return fstConfiguration.asByteArray(object);
    }

    /**
     * 使用FST的类匹配'猜测'功能读取序列化对象
     *
     * @param blob 序列化的字节数组对象
     * @return 反序列化的可序列化对象
     * @throws IOException 发生IO错误时
     * @throws ClassNotFoundException 找不到类时
     */
    public static Serializable read(byte[] blob) throws IOException, ClassNotFoundException {
        return (Serializable) fstConfiguration.getObjectInput(blob).readObject();
    }

    /**
     * 将对象读取为指定的类
     *
     * @param clz 假定的类类型
     * @param blob 序列化的字节数组对象
     * @return 反序列化的可序列化对象
     * @throws Exception 发生错误时
     */
    public static Serializable read(byte[] blob, Class clz) throws Exception {
        return (Serializable) fstConfiguration.getObjectInput(blob).readObject(clz);
    }

    /**
     * FST序列化为JSON格式 -- 简单对象（Map、Lists）不能完全转换为对应的JSON格式，
     * 所以这不能用于外部API。请使用write/read JsonAPI方法。
     *
     * @param object 要序列化的对象
     * @return JSON字符串
     */
    public static String writeJson(Serializable object) { return fstJsonConfiguration.asJsonString(object); }

    /**
     * 从JSON字符串解析对象
     *
     * @param json JSON字符串
     * @return 反序列化的对象
     * @throws IOException 发生IO错误时
     * @throws ClassNotFoundException 找不到类时
     */
    public static Serializable readJson(String json) throws IOException, ClassNotFoundException {
        return (Serializable) fstConfiguration.getObjectInput(json.getBytes()).readObject();
    }

    /**
     * 使用Gson序列化为JSON格式
     * 适用于外部API和标准JSON格式需求
     *
     * @param object 要序列化的对象
     * @return JSON字符串
     */
    public static String writeJsonAPI(Serializable object) { return new Gson().toJson(object); }

    /**
     * 从JSON字符串解析对象为HashMap
     *
     * @param json JSON字符串
     * @return 对象的HashMap表示
     * @throws IOException 发生IO错误时
     * @throws ClassNotFoundException 找不到类时
     */
    public static HashMap readJsonAPI(String json) throws IOException, ClassNotFoundException {
        return new Gson().fromJson(json, HashMap.class);
    }

    /**
     * 自定义LinkedHashMap序列化器
     *
     * FST在共享引用为false时处理LinkedHashSet有困难，而设置为true时性能会下降。
     * 因此使用自定义序列化器来解决这个问题。
     */
    static class FSTLinkedHasMapSerializer extends FSTBasicObjectSerializer {

        @Override
        public void writeObject(FSTObjectOutput out, Object toWrite, FSTClazzInfo clzInfo, FSTClazzInfo.FSTFieldInfo referencedBy, int streamPosition) throws IOException {
            LinkedHashMap map = (LinkedHashMap)toWrite;

            out.writeInt(map.size());

            for (Object o : map.entrySet()) {
                Map.Entry entry = (Map.Entry)o;
                out.writeObject(entry.getKey());
                out.writeObject(entry.getValue());
            }
        }

        public Object instantiate(Class objectClass, FSTObjectInput in, FSTClazzInfo serializationInfo, FSTClazzInfo.FSTFieldInfo referencee, int streamPosition) throws Exception {
            int size = in.readInt();
            LinkedHashMap map = new LinkedHashMap();

            for (int i = 0; i < size; ++i) {
                map.put(in.readObject(), in.readObject());
            }
            return map;
        }
    }
}
