/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.services;

import com.google.gson.Gson;
import com.mentalresonance.dust.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.Semaphore;

/**
 * Gson持久化服务：便利的持久化机制
 *
 * 基于Gson的JSON持久化实现，具有以下特点：
 *
 * 优点：
 * 1. 容错性强：Gson在反序列化时对类变化比较宽容（开发期间经常发生）
 * 2. 可读性好：JSON格式便于调试和查看
 * 3. 跨语言兼容：JSON格式可以被其他语言读取
 * 4. 版本兼容：对类结构变化有一定的容忍度
 *
 * 缺点：
 * 1. 性能较低：相比FST序列化性能较差
 * 2. 类型要求：Gson要求在反序列化时提供类信息，
 *    因此使用此服务的持久化Actor必须重写getSnapshotClass()方法
 *
 * <AUTHOR>
 */
@Slf4j
public class GsonPersistenceService implements PersistenceService {

    /**
     * 最大并发数：限制同时进行的持久化操作数量
     */
    private static final int MAX_CONCURRENT = 16;

    /**
     * 信号量：控制并发访问的信号量
     */
    static final Semaphore available = new Semaphore(MAX_CONCURRENT, true);

    /**
     * 单例实例：确保全局只有一个持久化服务实例
     */
    private static GsonPersistenceService gsonPersistenceService = null;

    /**
     * 快照存储路径：快照文件在文件系统中的存储位置
     */
    public static Path SNAPSHOTS;

    /**
     * 文件扩展名：快照文件的扩展名
     */
    private static String fileType;

    /**
     * Gson实例：用于JSON序列化和反序列化
     */
    private static Gson gson;

    /**
     * 哈希ID标志：向后兼容性设置，控制是否对ID进行哈希处理
     */
    public boolean isHashedId = true;

    /**
     * 私有构造函数：防止外部直接实例化
     */
    private GsonPersistenceService() {}

    /**
     * 工厂方法：创建持久化服务实例（单例模式）
     *
     * 确定快照的默认存储位置（用户主目录/dust-snapshots）
     * 快照存储目录按以下优先级确定：
     * 1. 系统属性 config.snapshots
     * 2. 环境变量 SNAPSHOTS
     * 3. 用户主目录
     *
     * @param directory 放置快照的目录
     * @param extension 快照文件使用的扩展名
     * @throws IOException 发生错误时
     * @return 通用的持久化服务实例
     */
    public static GsonPersistenceService create(String directory, String extension) throws IOException {
        if (null == gsonPersistenceService)
        {
            // 确保扩展名以点开头
            if (! extension.startsWith(".")) extension = ".".concat(extension);

            Path p = Paths.get(directory);

            fileType = extension;
            // 如果目录不存在则创建
            if (Files.notExists(p)) {
                Files.createDirectory(p);
            }
            SNAPSHOTS = p;

            log.info("快照存储位置：{}", p.toString());

            gson = new Gson();
            gsonPersistenceService = new GsonPersistenceService();
        }
        return gsonPersistenceService;
    }

    /**
     * 创建持久化服务，将持久化对象存储在指定目录中
     *
     * @param directory 存储对象的目录
     * @return 持久化服务实例
     * @throws IOException 文件系统错误时
     */
    public static GsonPersistenceService create(String directory) throws IOException {
        return create(directory, "json");
    }

    /**
     * 默认创建方法：使用默认配置创建持久化服务
     *
     * 按优先级使用以下目录作为快照存储位置：
     * 1. 系统属性 config.snapshots
     * 2. 环境变量 SNAPSHOTS
     * 3. 用户主目录下的 dust_snapshots
     *
     * 文件扩展名使用 .json
     *
     * @return 持久化服务实例
     * @throws IOException 发生错误时
     */
    public static GsonPersistenceService create() throws IOException {
        String directory =
                null != System.getProperty("config.snapshots") ? System.getProperty("config.snapshots") :
                null != System.getenv("SNAPSHOTS") ? System.getenv("SNAPSHOTS") :
                null != System.getProperty("user.home") ? "%s/dust_snapshots".formatted(System.getProperty("user.home")) : null;

        if (null == directory)
            throw new IOException("无法确定快照目录");
        return create(directory, "json");
    }

    /**
     * 对ID进行哈希处理：使用MD5算法对ID进行哈希
     *
     * @param id 要哈希的ID
     * @return 哈希后的ID字符串
     * @throws NoSuchAlgorithmException 哈希算法不存在时
     */
    private String hashId(String id) throws NoSuchAlgorithmException {
        return StringUtils.hash(id, "MD5");
    }

    /**
     * 将对象写入指定ID下的文件
     *
     * 文件名格式为：md5(id) + 扩展名（如果启用哈希）或 id + 扩展名
     *
     * @param id 执行写入操作的对象标识符
     * @param object 要保存的数据
     * @throws InterruptedException 被中断时
     * @throws NoSuchAlgorithmException 哈希算法名称错误时
     * @throws IOException 发生错误时
     */
    public void write(String id, Serializable object) throws InterruptedException, NoSuchAlgorithmException, IOException
    {
        // 获取信号量，控制并发访问
        available.acquire();

        try {
            // 将对象序列化为JSON字节数组
            byte[] blob = gson.toJson(object).getBytes();
            // 构建文件路径（根据配置决定是否对ID进行哈希）
            Path p = Paths.get(SNAPSHOTS + "/" + (isHashedId ? hashId(id) : id) + fileType);
            // 写入文件（创建或覆盖现有文件）
            Files.write(p, blob, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        }
        catch (Throwable e) {
            log.error(String.format("Write Snapshot: %s", e.getMessage()));
            e.printStackTrace();
            throw e;
        }
        finally {
            available.release();
        }
    }
    /**
     * Read the object at the given Id. This is here to match the interface but simply throws an exception since
     * we need to know the class we are de serializing to
     * @param id of object
     * @return deserialized object if serialization exists else null
     * @throws InterruptedException if interrupted
     * @throws IOException on error
     * @throws ClassNotFoundException Cannot find class to instantiate
     */
    public Serializable read(String id) throws Exception {
        throw new Exception("Generic read not supported. Use read(String id, Class<?> clz)");
    }

    /**
     * deSerialize object from file
     * @param id file identifier
     * @param clz to be created
     * @return deSerialized object
     * @throws Exception on error
     */
    @Override
    public Serializable read(String id, Class<?> clz) throws Exception {
        Serializable object;

        available.acquire();

        try {
            Path p = Paths.get(SNAPSHOTS + "/" + (isHashedId ? hashId(id) : id) + fileType);
            if (! Files.exists(p))
                object = null;
            else
                object = (Serializable) gson.fromJson(Files.readString(p), clz);
        }
        catch (Throwable e) {
            log.error(String.format("Read Snapshot: %s", e.getMessage()));
            throw e;
        }
        finally {
            available.release();
        }
        return object;
    }

    /**
     * Delete the object at id if it exists.
     * @param id of the object
     * @throws IOException on error
     * @throws InterruptedException if interrupted
     * @throws NoSuchAlgorithmException security
     */
    public void delete(String id) throws InterruptedException, IOException, NoSuchAlgorithmException {

        try {
            available.acquire();
            Path p = Paths.get(SNAPSHOTS + "/" + (isHashedId ? hashId(id) : id) + fileType);
            if (Files.exists(p))
                Files.delete(p);
        }
        catch (Exception e) {
            log.error("Delete Snapshot: %s".formatted(e.getMessage()));
            throw e;
        }
        finally {
            available.release();
        }
    }
}
