/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mentalresonance.dust.core.utils.StringUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.Semaphore;

/**
 * Jackson持久化服务：便利的持久化机制
 *
 * 基于Jackson的JSON持久化实现，具有以下特点：
 *
 * 优点：
 * 1. 容错性强：Jackson在反序列化时对类变化比较宽容（开发期间经常发生）
 * 2. 类型处理：与Gson相比，Jackson可以通过数据注解处理类型擦除
 * 3. 字段兼容：对序列化和反序列化之间添加/删除字段比较宽松
 * 4. 功能丰富：支持复杂的对象映射和自定义序列化
 *
 * 缺点：
 * 1. 性能较慢：Jackson比Gson稍慢，请根据需要选择合适的实现
 *
 * 使用建议：
 * - 开发阶段推荐使用Jackson，因为其对类变化的容忍度更高
 * - 生产环境如果对性能要求较高，可考虑使用Gson或FST
 *
 * <AUTHOR>
 */
@Slf4j
public class JacksonPersistenceService implements PersistenceService {

    /**
     * 最大并发数：限制同时进行的持久化操作数量
     */
    private static final int MAX_CONCURRENT = 16;

    /**
     * 信号量：控制并发访问的信号量
     */
    static final Semaphore available = new Semaphore(MAX_CONCURRENT, true);

    /**
     * 单例实例：确保全局只有一个持久化服务实例
     */
    private static JacksonPersistenceService jacksonPersistenceService = null;

    /**
     * 快照存储路径：快照文件在文件系统中的存储位置
     */
    public static Path SNAPSHOTS;

    /**
     * 文件扩展名：快照文件的扩展名
     */
    private static String fileType;

    /**
     * Jackson对象映射器：用于JSON序列化和反序列化
     */
    private static ObjectMapper objectMapper;

    /**
     * 哈希ID标志：向后兼容性设置，控制是否对ID进行哈希处理
     */
    public boolean isHashedId = true;

    /**
     * 私有构造函数：防止外部直接实例化
     */
    private JacksonPersistenceService() {}

    /**
     * 工厂方法：创建持久化服务实例（单例模式）
     *
     * 确定快照的默认存储位置（用户主目录/dust-snapshots）
     * 快照存储目录按以下优先级确定：
     * 1. 系统属性 config.snapshots
     * 2. 环境变量 SNAPSHOTS
     * 3. 用户主目录
     *
     * @param directory 放置快照的目录
     * @param extension 快照文件使用的扩展名
     * @throws IOException 发生错误时
     * @return Jackson持久化服务实例
     */
    public static JacksonPersistenceService create(String directory, String extension) throws IOException {
        if (null == jacksonPersistenceService)
        {
            // 确保扩展名以点开头
            if (! extension.startsWith(".")) extension = ".".concat(extension);

            Path p = Paths.get(directory);

            fileType = extension;
            // 如果目录不存在则创建
            if (Files.notExists(p)) {
                Files.createDirectory(p);
            }
            SNAPSHOTS = p;

            log.info("快照存储位置：{}", p.toString());

            objectMapper = new ObjectMapper();
            jacksonPersistenceService = new JacksonPersistenceService();
        }
        return jacksonPersistenceService;
    }

    /**
     * Create a PersistenceService storing persisted objects in directory
     * @param directory to store object in
     * @return the persistenceService
     * @throws IOException on file system errors
     */
    public static JacksonPersistenceService create(String directory) throws IOException {
        return create(directory, "json");
    }

    /**
     * Default. Use first of property config.snapshots, env SNAPSHOTS or ~ as the directory and .json
     * as the extension
     * @return the persistence service
     * @throws IOException on error
     */
    public static JacksonPersistenceService create() throws IOException {
        String directory =
                null != System.getProperty("config.snapshots") ? System.getProperty("config.snapshots") :
                null != System.getenv("SNAPSHOTS") ? System.getenv("SNAPSHOTS") :
                null != System.getProperty("user.home") ? "%s/dust_snapshots".formatted(System.getProperty("user.home")) : null;

        if (null == directory)
            throw new IOException("Cannot determine snapshots directory");
        return create(directory, "json");
    }

    private String hashId(String id) throws NoSuchAlgorithmException {
        return StringUtils.hash(id, "MD5");
    }
    /**
     * Write the object under the given id. Filenames are md5(id).snap
     *
     * @param id identifier of object doing the writing
     * @param object data to be saved.
     * @throws InterruptedException on interruption
     * @throws NoSuchAlgorithmException bad hash algorithm name
     * @throws IOException on errors
     */
    public void write(String id, Serializable object) throws InterruptedException, NoSuchAlgorithmException, IOException
    {
        available.acquire();

        try {
            byte[] blob = objectMapper.writeValueAsBytes(object);
            Path p = Paths.get(SNAPSHOTS + "/" + (isHashedId ? hashId(id) : id) + fileType);
            Files.write(p, blob, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        }
        catch (Throwable e) {
            log.error(String.format("Write Snapshot: %s", e.getMessage()));
            e.printStackTrace();
            throw e;
        }
        finally {
            available.release();
        }
    }
    /**
     * Read the object at the given Id. This is here to match the interface but simply throws an exception since
     * we need to know the class we are de serializing to
     * @param id of object
     * @return deserialized object if serialization exists else null
     * @throws InterruptedException if interrupted
     * @throws IOException on error
     * @throws ClassNotFoundException Cannot find class to instantiate
     */
    public Serializable read(String id) throws Exception {
        throw new Exception("Generic read not supported. Use read(String id, Class<?> clz)");
    }

    /**
     * deSerialize object from file
     * @param id file identifier
     * @param clz to be created
     * @return deSerialized object
     * @throws Exception on error
     */
    @Override
    public Serializable read(String id, Class<?> clz) throws Exception {
        Serializable object;

        available.acquire();

        try {
            Path p = Paths.get(SNAPSHOTS + "/" + (isHashedId ? hashId(id) : id) + fileType);
            if (! Files.exists(p))
                object = null;
            else
                object = (Serializable) objectMapper.readValue(Files.readAllBytes(p), clz);
        }
        catch (Throwable e) {
            log.error(String.format("Read Snapshot: %s", e.getMessage()));
            throw e;
        }
        finally {
            available.release();
        }
        return object;
    }

    /**
     * Delete the object at id if it exists.
     * @param id of the object
     * @throws IOException on error
     * @throws InterruptedException if interrupted
     * @throws NoSuchAlgorithmException security
     */
    public void delete(String id) throws InterruptedException, IOException, NoSuchAlgorithmException {

        try {
            available.acquire();
            Path p = Paths.get(SNAPSHOTS + "/" + (isHashedId ? hashId(id) : id) + fileType);
            if (Files.exists(p))
                Files.delete(p);
        }
        catch (Exception e) {
            log.error("Delete Snapshot: %s".formatted(e.getMessage()));
            throw e;
        }
        finally {
            available.release();
        }
    }
}
