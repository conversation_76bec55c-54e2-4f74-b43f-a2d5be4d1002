/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.utils;

import java.io.Serializable;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

/**
 * 简单的LRU缓存实现：最近最少使用缓存
 *
 * 这个实现不是线程安全的，因此可以在Actor中使用而不会有线程安全的开销。
 * 基于LinkedHashMap实现，支持访问顺序和插入顺序两种淘汰策略。
 *
 * 主要特性：
 * 1. 自动淘汰：当缓存大小超过限制时自动移除最老的条目
 * 2. 两种排序：支持访问顺序（LRU）和插入顺序（FIFO）
 * 3. 可序列化：支持持久化和网络传输
 * 4. Map接口：完全兼容Java Map接口
 * 5. 高性能：基于LinkedHashMap的O(1)操作
 *
 * @param <A> 键的类型
 * @param <B> 值的类型
 */
public class LRUCache<A, B> implements Serializable, Map<A, B> {
    /**
     * 缓存中的最大条目数：公开以便序列化
     */
    final int maxEntries;

    /**
     * 排序方式标志：如果为true则使用访问顺序，否则使用插入顺序进行老化淘汰
     */
    final boolean access;

    /**
     * 底层缓存实现：基于LinkedHashMap的缓存存储
     */
    final LinkedHashMap<A, B> _cache;

    /**
     * 默认构造函数：使用访问顺序（LRU策略）
     *
     * @param maxEntries 缓存大小限制
     */
    public LRUCache(final int maxEntries) {
        this(maxEntries, true);
    }

    /**
     * 完整构造函数：可指定排序方式
     *
     * @param maxEntries 超过此大小时开始清理的阈值
     * @param access 排序方式 - '最老的'被清理：true为访问顺序，false为插入顺序
     */
    public LRUCache(final int maxEntries, boolean access) {
        _cache = new LinkedHashMap<>(maxEntries + 1, 0.75f, access) {
            @Override
            protected boolean removeEldestEntry(final Map.Entry<A, B> eldest) {
                return super.size() > maxEntries;
            }
        };
        this.maxEntries = maxEntries;
        this.access = access;
    }

    /**
     * 创建底层缓存的浅拷贝
     *
     * @return LinkedHashMap的克隆副本
     */
    public LinkedHashMap<A, B> shallowCopy() {
        return new LinkedHashMap<>(_cache);
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存中的元素数量
     */
    public int size() {
        return _cache.size();
    }

    /**
     * 检查缓存是否为空
     *
     * @return 如果为空返回true，否则返回false
     */
    public boolean isEmpty() {
        return _cache.isEmpty();
    }

    /**
     * 检查缓存是否包含指定键
     *
     * @param key 要检查的键
     * @return 如果缓存包含此键的元素则返回true
     */
    public boolean containsKey(Object key) {
        return _cache.containsKey(key);
    }

    /**
     * Does cache contain value
     * @param value  the value to be test
     * @return true ifd an entry in the map with this value. Test uses both == and equals()
     */
    public boolean containsValue(Object value) {
        return _cache.containsValue(value);
    }

    /**
     * Return the object with given key else null. Note that null is a valid value
     * @param key the key
     * @return the value which may be null. If no key matches then return null.
     */
    public B get(Object key) {
        return _cache.get(key);
    }

    /**
     * Puts the value in the cache under key. Any previous value is replaced.
     * @param key The key
     * @param value The value
     * @return The previous value if it had one else null.
     */
    public B put(A key, B value) {
        return _cache.put(key, value);
    }


    /**
     * Remove object under key if it exists. Returns removed object or null if the key had nothing associated.
     * @param key The key
     * @return removed object or null
     */
    public B remove(Object key) {
        return _cache.remove(key);
    }

    /**
     * Put all elements of the map into the cache
     * @param m the map
     */
    public void putAll(Map<? extends A, ? extends B> m) {
        _cache.putAll(m);
    }

    /**
     * Clear the cache
     */
    public void clear() {  _cache.clear(); }

    /**
     * Return underlying map
     * @return the cache map
     */
    public LinkedHashMap<A, B> map() { return _cache; }

    /**
     * Get keySet of map
     * @return the keySet
     */
    public Set<A> keySet() {
        return _cache.keySet();
    }

    /**
     * Return values in the map
     * @return values
     */
    public Collection<B> values() {
        return _cache.values();
    }

    /**
     * Return EntrySet of the map
     * @return EntrySet
     */
    public Set<Map.Entry<A, B>> entrySet() {
        return _cache.entrySet();
    }
}

