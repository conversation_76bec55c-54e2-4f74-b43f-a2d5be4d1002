/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.utils;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 随机数工具类：提供便利的随机数操作
 *
 * 这个工具类提供了常用的随机数生成和随机选择功能，
 * 使用ThreadLocalRandom确保线程安全和高性能。
 *
 * 主要特性：
 * 1. 线程安全：使用ThreadLocalRandom避免线程竞争
 * 2. 高性能：比Random类性能更好
 * 3. 简单易用：提供便利的静态方法
 * 4. 泛型支持：支持任意类型的随机选择
 *
 * <AUTHOR>
 */
public class RandomUtils {

    /**
     * 私有构造函数：防止实例化工具类
     */
    private RandomUtils() {}

    /**
     * 从列表中返回随机元素
     *
     * 如果列表为空则返回null，使用者需要注意空值检查。
     * 使用ThreadLocalRandom确保线程安全和高性能。
     *
     * @param list 要选择的列表
     * @param <E> 列表元素类型
     * @return 随机选择的元素，如果列表为空则返回null
     */
    public static <E> E chooseOne(List<E> list) {
        int size = list.size();

        if (size > 0) {
            return list.get(ThreadLocalRandom.current().nextInt(0, size));
        } else {
            return null;
        }
    }
}
