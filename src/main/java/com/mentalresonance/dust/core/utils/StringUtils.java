/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 字符串工具类：提供有用的字符串处理功能
 *
 * 这个工具类提供了常用的字符串操作方法，主要包括：
 * 1. 字符串哈希功能：支持多种哈希算法
 * 2. 字节数组到十六进制字符串的转换
 * 3. 其他字符串处理工具方法
 *
 * 所有方法都是静态的，类不能被实例化。
 *
 * <AUTHOR>
 */
public class StringUtils {

    /**
     * 私有构造函数：防止实例化工具类
     */
    private StringUtils() {}

    /**
     * 对字符串进行哈希处理
     *
     * 使用指定的哈希算法对输入字符串进行哈希，并返回十六进制表示。
     * 常用的算法包括：MD5、SHA-1、SHA-256等。
     *
     * @param toHash 要哈希的字符串
     * @param algorithm 使用的哈希算法名称（如"MD5"、"SHA-256"）
     * @return 哈希后的十六进制字符串表示
     * @throws NoSuchAlgorithmException 当指定的算法不存在时
     */
    public static String hash(String toHash, String algorithm) throws NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance(algorithm);
        byte[] encodedhash = digest.digest(toHash.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(encodedhash);
    }

    /**
     * 将字节数组转换为十六进制字符串表示
     *
     * 每个字节转换为两位十六进制字符，不足两位的前面补0。
     * 例如：字节数组 [255, 0, 15] 转换为 "ff000f"
     *
     * @param hash 要转换的字节数组
     * @return 十六进制字符串表示
     */
    public static String bytesToHex(byte[] hash) {
        StringBuilder hexString = new StringBuilder(2 * hash.length);
        for (int i = 0; i < hash.length; i++) {
            String hex = Integer.toHexString(0xff & hash[i]);
            if(hex.length() == 1) {
                hexString.append('0');  // 补零确保每个字节占两位
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
