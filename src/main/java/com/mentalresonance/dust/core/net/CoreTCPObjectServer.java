/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.net;

import com.mentalresonance.dust.core.services.SerializationService;
import com.mentalresonance.dust.core.system.ActorSystemConnectionManager;
import lombok.extern.slf4j.Slf4j;
import org.nustaq.net.TCPObjectSocket;
import org.nustaq.serialization.FSTConfiguration;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.CompletableFuture;

/**
 * 核心TCP对象服务器：运行服务器以接收远程消息
 *
 * 此服务器为每个新连接生成一个新的虚拟线程来处理，但每个线程保持连接开放。
 * 这种设计允许高并发的连接处理，同时保持连接的持久性。
 *
 * 主要特性：
 * 1. 基于虚拟线程的高并发处理
 * 2. 持久连接管理
 * 3. FST序列化支持
 * 4. 连接池管理集成
 * 5. 优雅的关闭机制
 *
 * 基于reudi的TCPObjectServer实现
 */
@Slf4j
public class CoreTCPObjectServer {

    /**
     * 欢迎套接字：用于接受新的客户端连接
     */
    ServerSocket welcomeSocket;

    /**
     * FST序列化配置：用于对象的序列化和反序列化
     */
    final FSTConfiguration conf;

    /**
     * 监听端口：服务器监听的端口号
     */
    final int port;

    /**
     * 终止标志：控制服务器的运行状态
     */
    volatile boolean terminated;

    /**
     * Actor系统连接管理器：管理到远程Actor系统的连接
     */
    final ActorSystemConnectionManager actorSystemConnectionManager;

    /**
     * 停止完成标志：当服务器停止时完成此Future
     */
    final CompletableFuture<Boolean> haveStopped;

    /**
     * 服务器线程：运行服务器主循环的线程
     */
    Thread serverThread;

    /**
     * 构造函数：创建TCP对象服务器
     *
     * @param conf 序列化配置
     * @param port 监听端口号
     * @param actorSystemConnectionManager 管理传入连接的连接管理器
     * @param haveStopped 停止时完成的Future
     */
    public CoreTCPObjectServer(
            FSTConfiguration conf,
            int port,
            ActorSystemConnectionManager actorSystemConnectionManager,
            CompletableFuture<Boolean> haveStopped) {
        this.conf = conf;
        this.port = port;
        this.actorSystemConnectionManager = actorSystemConnectionManager;
        this.haveStopped = haveStopped;
    }

    /**
     * 使用给定的监听器启动服务器
     *
     * @param listener 新客户端连接的监听器
     * @throws IOException 发生错误时
     */
    public void start(final org.nustaq.net.TCPObjectServer.NewClientListener listener) throws IOException {
        final ActorSystemConnectionManager connectionManager = actorSystemConnectionManager;
        serverThread = new Thread("服务器-端口" + port) {
            public void run() {
                try {
                    welcomeSocket = new ServerSocket(port);
                    log.info("TCP对象服务器已启动，监听端口：{}", port);

                    while (!terminated)
                    {
                        // 接受新的客户端连接
                        final Socket connectionSocket = welcomeSocket.accept();
                        log.debug("接受新连接：{}", connectionSocket.getRemoteSocketAddress());

                        // 将连接添加到连接管理器
                        connectionManager.addRemoteSocket(connectionSocket);

                        // 为每个连接启动虚拟线程处理
                        Thread.startVirtualThread(new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    listener.connectionAccepted(new TCPObjectSocket(connectionSocket,conf));
                                } catch (IOException e) {
                                    log.error("处理客户端连接时发生错误：{}", e.getMessage(), e);
                                }
                            }
                        });
                    }
                }
                catch (Exception e) {
                    if (!terminated) {
                        log.error("服务器运行时发生异常：{}", e.getMessage(), e);
                    }
                }
                finally {
                    try {
                        welcomeSocket.close();
                    } catch (IOException e) {
                        log.error("Could not close welcomeSocket " + e.getMessage());
                    } finally {
                        haveStopped.complete(true);
                    }
                }
            }
        };
        serverThread.start();
    }

    /**
     * Stops the server. The assumption here is the ActorSystem (and hence the application) is shutting down
     * The server will close this connection so we don't.
     */
    public void stop() {
        try {
            log.info("Stopping server on port " + port);
            terminated = true;
            TCPObjectSocket socket = new TCPObjectSocket("localhost", port, SerializationService.getFstConfiguration());
            socket.writeObject(null);
            socket.flush();
            serverThread.interrupt();
        }
        catch (Exception e) {
            log.error("Stopping server: %s".formatted(e.getMessage()));
        }
    }
}
