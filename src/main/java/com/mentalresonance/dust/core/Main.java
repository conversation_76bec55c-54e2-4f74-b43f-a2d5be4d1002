package com.mentalresonance.dust.core;

/**
 * Dust-Core框架主入口类
 *
 * Dust-Core是一个基于Actor模型的分布式系统框架，提供：
 * 1. 轻量级Actor实现，支持本地和远程通信
 * 2. 高性能消息传递机制
 * 3. 灵活的监督策略和错误恢复
 * 4. 多种持久化方案支持
 * 5. 完整的生命周期管理
 *
 * 使用示例：
 * ActorSystem system = new ActorSystem("MySystem");
 * ActorRef actor = system.getContext().actorOf(MyActor.props(), "myActor");
 * actor.tell(new StartMsg(), ActorRef.noSender());
 */
public class Main {
    public static void main(String[] args) {
        System.out.println("欢迎使用Dust-Core Actor框架！");
        System.out.println("这是一个基于Actor模型的高性能分布式系统框架。");
        System.out.println("请参考文档了解如何使用此框架构建您的应用程序。");
    }
}
