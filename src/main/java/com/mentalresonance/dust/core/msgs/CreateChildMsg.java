/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import com.mentalresonance.dust.core.actors.lib.entities.PodManagerActor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 接收者将创建具有给定名称的子级。如果msg不为null，则将消息发送给新子级。
 * 接收者负责知道要创建什么，通常是{@link PodManagerActor}
 *
 *  <AUTHOR>
 */
@Getter
public class CreateChildMsg implements Serializable {
    /**
     * 要给子级的名称
     */
    final String name;

    /**
     * 要发送给子级的可选消息
     */
    final Serializable msg;

    /**
     * 构造函数
     * @param name 子级的名称
     */
    public CreateChildMsg(String name) {
        this.name = name;
        msg = null;
    }

    /**
     * 构造函数
     * @param name 子级的名称
     * @param msg 创建后发送给子级的消息
     */
    public CreateChildMsg(String name, Serializable msg) {
        this.name = name;
        this.msg = msg;
    }

    /**
     * 当被死信/pod管理器处理时，知道该消息很有用
     * @return
     */
    @Override
    public String toString() {
        return "CreateChildMsg 名称:" + name + " 消息:" + msg;
    }
}