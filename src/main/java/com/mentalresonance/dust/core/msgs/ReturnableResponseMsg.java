/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import com.mentalresonance.dust.core.actors.ActorRef;

/**
 * ReturnableResponse总是知道是谁发送的，因此接收Actor不必跟踪它，并且它包含
 * 响应或异常的槽位
 *
 * @param <T> 响应类型
 *
 * <AUTHOR>
 */
public class ReturnableResponseMsg<T> extends ReturnableMsg {

    /**
     * 如果发生异常
     */
    public Exception exception = null;
    /**
     * 如果成功则响应
     */
    public T response = null;

    /**
     * 构造函数
     * @param sender 声称发送此消息的人
     */
    public ReturnableResponseMsg(ActorRef sender) { super(sender); }
}