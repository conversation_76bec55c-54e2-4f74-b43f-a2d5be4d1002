/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 便利消息，用于那些可能需要传递状态的Actor。
 * 如果状态没有被克隆，通常不建议这样做 - 或者您信任您的客户端...
 * @param <S>
 */
@Getter
@Setter
public class GetStateMsg<S> implements Serializable {

    S state;
}