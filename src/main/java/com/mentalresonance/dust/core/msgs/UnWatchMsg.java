/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import java.io.Serializable;

/**
 * 取消监视消息：如果发送者在接收者的监视列表中，则将其移除
 *
 * 这是Actor监视机制的配套消息，用于取消之前建立的监视关系。
 * 发送此消息后，发送者将不再接收被监视Actor的Terminated消息。
 *
 * 使用场景：
 * 1. 主动取消不再需要的监视关系
 * 2. 清理临时的监视关系
 * 3. 优化系统性能，减少不必要的通知
 * 4. 实现动态的依赖管理
 *
 * 取消监视特点：
 * - 主动取消：由监视者主动发起
 * - 即时生效：消息处理后立即生效
 * - 安全操作：即使没有监视关系也不会出错
 * - 对称操作：与WatchMsg形成对称的操作对
 *
 * <AUTHOR>
 */
public class UnWatchMsg implements Serializable {
    /**
     * 默认构造函数：创建取消监视消息实例
     *
     * 取消监视消息不需要额外的参数，发送者信息由消息传递系统自动提供。
     */
    public UnWatchMsg() {}
}
