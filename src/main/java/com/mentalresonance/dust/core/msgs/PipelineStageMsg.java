/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import lombok.Getter;

import java.io.Serializable;

/**
 * 要发送到管道的消息。如果它具有指定的阶段，则PipelineStage消息
 * 会被拆包，msg会被传递到该阶段（好像来自发送者）
 */
@Getter
public class PipelineStageMsg implements Serializable {
    /**
     * 目标阶段的名称
     */
    final String stage;
    /**
     * 要发送到目标阶段的消息
     */
    final Serializable msg;

    /**
     * 构造函数
     * @param stage 名称
     * @param msg 要发送到阶段的消息
     */
    public PipelineStageMsg(String stage, Serializable msg) {
        this.stage = stage;
        this.msg = msg;
    }

}