/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import lombok.Getter;

import java.io.Serializable;

/**
 * 终止消息：Actor停止后发送给所有监视者的消息
 *
 * 当一个Actor停止时，会自动向所有监视它的Actor发送此消息。
 * 包含已终止Actor的便利名称，方便那些按名称管理的监视者使用
 * （避免解析sender字段）。
 *
 * 使用场景：
 * 1. 监视子Actor的生命周期
 * 2. 实现Actor的依赖管理
 * 3. 触发清理或重启逻辑
 * 4. 维护Actor的引用计数
 *
 * <AUTHOR>
 */
@Getter
public class Terminated implements Serializable {
    /**
     * 已终止Actor的名称：便于监视者识别是哪个Actor终止了
     */
    final String name;

    /**
     * 构造函数：创建终止消息
     *
     * @param name 已终止Actor的名称
     */
    public Terminated(String name) {
        this.name = name;
    }
}
