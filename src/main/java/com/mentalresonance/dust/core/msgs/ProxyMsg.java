/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.ifc.IKeyed;
import lombok.Getter;
import lombok.Setter;

/**
 * 代理消息是可能被发送到中间Actor（例如节流器）的消息，但随后需要发送到目标。
 *
 * 代理对消息执行其操作，然后将其作为来自发送者发送到目标。如果目标为null，
 * 则将其发送回发送者
 *
 * <AUTHOR>
 */
public class ProxyMsg extends ReturnableMsg implements IKeyed {

    /**
     * 此消息是否已通过代理
     */
    @Setter
    @Getter
    boolean proxied = false;
    /**
     * 消息的最终目标。
     */
    public ActorRef target = null;

    /**
     * 构造函数
     * @param sender 此消息的发送者
     */
    public ProxyMsg(ActorRef sender) {
        super(sender);
    }

    /**
     * 构造函数
     * @param sender 此消息的发送者
     * @param target 此消息的目标
     */
    public ProxyMsg(ActorRef sender, ActorRef target) {
        super(sender);
        this.target = target;
    }

}