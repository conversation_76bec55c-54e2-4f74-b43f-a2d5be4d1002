/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import com.mentalresonance.dust.core.actors.PersistentActor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 快照失败消息：如果快照尝试失败，则发送给{@link PersistentActor}
 *
 * 这是持久化机制的错误反馈消息，通知Actor其状态保存失败。
 * 包含导致失败的异常信息，帮助Actor进行错误处理和恢复。
 *
 * Actor收到此消息后可以：
 * 1. 记录错误日志
 * 2. 重试快照保存
 * 3. 切换到备用持久化策略
 * 4. 通知上级Actor或用户
 * 5. 进入降级模式
 *
 * 消息特点：
 * - 错误详情：包含具体的异常信息
 * - 异步通知：不阻塞Actor的正常处理
 * - 可操作性：提供足够信息进行错误恢复
 * - 可靠传递：通过Actor消息系统传递
 *
 * <AUTHOR>
 */
@Getter
public class SnapshotFailureMsg implements Serializable {
    /**
     * 导致失败的异常：描述失败原因的异常对象
     */
    final Exception exception;

    /**
     * 构造函数：创建快照失败消息实例
     *
     * @param e 描述失败的异常
     */
    public SnapshotFailureMsg(Exception e) {
        this.exception = e;
    }

}
