/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import com.mentalresonance.dust.core.actors.ActorRef;
import lombok.Getter;

import java.io.Serializable;

/**
 * 在父级的SupervisionStrategy处理完子级异常后，由Actor发送给自己
 *
 *  <AUTHOR>
 */
public class ChildExceptionMsg implements Serializable {
    /**
     * 抛出异常的子级
     */
    @Getter
    final
    ActorRef child;
    /**
     * 异常
     */
    @Getter
    final
    Throwable exception;

    /**
     * 构造函数
     * @param child 抛出异常的子级
     * @param exception 异常
     */
    public ChildExceptionMsg(ActorR<PERSON> child, Throwable exception) {
        this.child = child;
        this.exception = exception;
    }

    @Override
    public String toString() {
        return "来自子级的ChildExceptionMsg: " + child + " 异常: " + exception.getMessage();
    }
}