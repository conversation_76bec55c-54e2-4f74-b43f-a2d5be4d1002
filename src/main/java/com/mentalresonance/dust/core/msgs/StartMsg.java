/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import lombok.Getter;

import java.io.Serializable;

/**
 * 启动消息：通用的启动信号消息
 *
 * 这是一个便利的消息类型，具体含义由接收者决定。
 * 通常用于通知Actor开始执行某些操作或进入特定状态。
 *
 * <AUTHOR>
 */
@Getter
public class StartMsg implements Serializable {

    /**
     * 可选的附加消息：可以携带额外的启动参数或配置信息
     */
    final Serializable msg;

    /**
     * 默认构造函数：创建不带附加消息的启动消息
     */
    public StartMsg() { msg = null; }

    /**
     * 带参数的构造函数：创建携带附加消息的启动消息
     *
     * @param msg 包装的附加消息
     */
    public StartMsg(Serializable msg) { this.msg = msg; }
}
