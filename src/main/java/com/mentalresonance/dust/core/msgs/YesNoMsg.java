/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import java.io.Serializable;

/**
 * 是否消息：便利的消息类型，具体含义由接收者决定
 *
 * 这是一个通用的二元选择消息，用于表示布尔值的决策或状态。
 *
 * 使用场景：
 * 1. 确认/拒绝操作：用户确认或拒绝某个操作
 * 2. 状态查询：查询某个条件是否满足
 * 3. 权限检查：检查是否有权限执行某个操作
 * 4. 配置开关：表示某个功能的开启或关闭状态
 * 5. 投票机制：在分布式决策中表示赞成或反对
 *
 * 设计特点：
 * - 简单明确：只包含一个布尔值
 * - 语义清晰：yes字段名比boolean更直观
 * - 不可变：final字段确保消息不可修改
 * - 轻量级：最小的内存占用
 *
 * <AUTHOR>
 */
public class YesNoMsg implements Serializable {

    /**
     * 是否标志：true表示是/同意/成功，false表示否/拒绝/失败
     */
    public final boolean yes;

    /**
     * 构造函数：创建是否消息
     *
     * @param yes true表示是，false表示否
     */
    public YesNoMsg(boolean yes) { this.yes = yes; }
}
