/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;


import com.mentalresonance.dust.core.actors.lib.PubSubActor;

import java.io.Serializable;

/**
 * (取消)订阅特定类消息的消息。发送给{@link PubSubActor}以指示发送者
 * 有兴趣接收任何给定类的消息。
 *
 * <AUTHOR>
 */
public class PubSubMsg implements Serializable {
    /**
     * 如果为true则订阅，否则取消订阅
     */
    public Boolean subscribe = true;
    /**
     * 我们正在(取消)订阅的完全限定类名
     */
    public final String fqn;

    /**
     * 订阅消息
     * @param clz - 要订阅的消息类
     */
    public PubSubMsg(Class<? extends Serializable> clz) {
        this.fqn = clz.getName();
    }

    /**
     * (取消)订阅消息
     * @param clz - 要(取消)订阅的消息类
     * @param subscribe - 如果为true则订阅，否则取消订阅
     */
    public PubSubMsg(Class<? extends Serializable> clz, Boolean subscribe) {
        this.fqn = clz.getName();
        this.subscribe = subscribe;
    }
}