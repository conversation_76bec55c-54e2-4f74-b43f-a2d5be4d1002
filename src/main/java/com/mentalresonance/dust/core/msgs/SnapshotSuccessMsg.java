/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import com.mentalresonance.dust.core.actors.PersistentActor;

import java.io.Serializable;
/**
 * 快照成功消息：如果快照尝试成功，则发送给{@link PersistentActor}
 *
 * 这是持久化机制的反馈消息，通知Actor其状态已成功保存到持久化存储中。
 * Actor可以根据此消息来：
 * 1. 确认状态已安全保存
 * 2. 清理临时数据
 * 3. 更新内部状态标记
 * 4. 触发后续的业务逻辑
 *
 * 消息特点：
 * - 异步通知：不阻塞快照保存过程
 * - 确认机制：提供操作成功的确认
 * - 简单设计：不携带额外数据
 * - 可靠传递：通过Actor消息系统传递
 *
 * <AUTHOR>
 */
public class SnapshotSuccessMsg implements Serializable {
    /**
     * 默认构造函数：创建快照成功消息实例
     *
     * 成功消息不需要额外的参数，仅作为成功状态的通知。
     */
    public SnapshotSuccessMsg() {}
}
