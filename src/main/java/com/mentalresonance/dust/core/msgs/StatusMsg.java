/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import lombok.Getter;

import java.io.Serializable;

/**
 * 状态消息：用于传递通用状态信息的简单类
 *
 * 当'客户端'Actor需要知道'服务器'Actor已经完成请求时很有用，
 * 可用于同步或其他目的。
 *
 * 使用场景：
 * 1. 异步操作的完成通知
 * 2. 请求-响应模式的状态反馈
 * 3. 服务调用的成功/失败通知
 * 4. 带标签的状态跟踪
 *
 * 支持可选的标签机制，便于请求和响应的对齐匹配。
 */
@Getter
public class StatusMsg implements Serializable {
    /**
     * 可选的'标记'：用于请求/响应的对齐匹配
     * 可以是任何可序列化的对象，用于标识特定的请求
     */
    Serializable tag = null;

    /**
     * 成功标志：true表示成功，false表示失败
     */
    boolean success;

    /**
     * 可选的附加消息：提供详细的状态描述或错误信息
     */
    String message = null;

    /**
     * 构造函数：创建只包含成功标志的状态消息
     *
     * @param success 操作是否成功
     */
    public StatusMsg(boolean success) {
        this.success = success;
    }

    /**
     * 构造函数：创建包含成功标志和消息的状态消息
     *
     * @param success 操作是否成功
     * @param message 状态描述消息
     */
    public StatusMsg(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    /**
     * 构造函数：创建包含所有字段的完整状态消息
     *
     * @param success 操作是否成功
     * @param message 状态描述消息
     * @param tag 用于请求/响应匹配的标签
     */
    public StatusMsg(boolean success, String message, Serializable tag) {
        this.success = success;
        this.message = message;
        this.tag = tag;
    }
}
