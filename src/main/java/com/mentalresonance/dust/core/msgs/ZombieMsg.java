/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import java.io.Serializable;

/**
 * 僵尸消息接口：不会被死信Actor记录日志的消息标记接口
 *
 * 这个接口标记那些已知可能因为完全正当的原因而成为死信的消息。
 *
 * 使用场景：
 * 1. 系统关闭：在系统关闭过程中发送的消息
 * 2. 清理操作：向可能已死亡的Actor发送清理消息
 * 3. 状态检查：检查Actor是否仍然存活的探测消息
 * 4. 测试场景：测试死信处理机制时使用
 *
 * 设计目的：
 * - 减少日志噪音：避免在日志中产生大量的死信记录
 * - 区分消息类型：区分正常死信和预期的僵尸消息
 * - 系统优化：提高系统关闭和清理的效率
 *
 * 实现方式：
 * 消息类实现此接口后，DeadLetterActor会特别处理，不会为其生成警告日志。
 *
 * 使用示例：
 * ```java
 * public class CleanupMsg implements Serializable, ZombieMsg {
 *     // 清理消息实现
 * }
 * ```
 */
public interface ZombieMsg extends Serializable {
}
