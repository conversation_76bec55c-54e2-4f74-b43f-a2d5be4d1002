/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.system.DeadLetterActor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 死信消息：包装发送给不存在Actor的消息
 *
 * 当消息被发送到一个不存在的Actor时，该消息会被包装在DeadLetter中，
 * 然后发送到{@link DeadLetterActor}进行处理。
 *
 * 死信机制的作用：
 * 1. 防止消息丢失：记录无法投递的消息
 * 2. 调试支持：帮助开发者发现路径错误或时序问题
 * 3. 监控告警：可以监控死信数量来发现系统问题
 * 4. 消息恢复：某些情况下可以重新路由死信消息
 *
 * <AUTHOR>
 */
public class DeadLetter extends ReturnableMsg
{
    /**
     * 被发送的原始消息：无法投递的消息内容
     */
    @Getter
    final Serializable message;

    /**
     * 目标路径：消息原本要发送到的Actor路径
     */
    @Getter
    final String path;

    /**
     * 发送者：发送此消息的Actor引用
     */
    @Getter
    final ActorRef sender;

    /**
     * 构造函数：创建死信消息
     *
     * @param message 被发送的消息
     * @param path 不存在的目标Actor路径
     * @param sender 发送消息的Actor
     */
    public DeadLetter(Serializable message, String path, ActorRef sender) {
        super(sender);
        this.message = message;
        this.path = path;
        this.sender = sender;
    }

    /**
     * 用于日志记录的字符串表示
     *
     * @return 日志字符串表示
     */
    @Override
    public String toString() {
        return String.format("死信：消息%s由%s发送到%s", message, sender, path);
    }
}
