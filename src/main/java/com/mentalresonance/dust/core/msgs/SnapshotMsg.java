/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import lombok.Getter;

import java.io.Serializable;

/**
 * 快照消息：在恢复过程中发送给持久化Actor的消息
 *
 * 此消息包含最后持久化的快照数据，即使快照为null也总是会发送。
 * null值可能表示：
 * 1. 之前没有保存任何数据
 * 2. 保存的数据本身就是null
 *
 * 无法区分这两种情况，这是设计上的权衡。
 *
 * 快照消息是持久化Actor恢复流程的核心部分：
 * 1. Actor启动时首先接收SnapshotMsg
 * 2. Actor根据快照数据恢复状态
 * 3. 调用postRecovery()完成恢复
 * 4. 然后进入正常的preStart()流程
 *
 * <AUTHOR>
 */
public class SnapshotMsg implements Serializable {
    /**
     * 持久化的快照数据：包含Actor之前保存的状态
     * 可能为null，表示没有之前的状态或状态本身为null
     */
    @Getter
    final Serializable snapshot;

    /**
     * 构造函数：创建快照消息
     *
     * @param snapshot 恢复的快照数据，可能为null
     */
    public SnapshotMsg(Serializable snapshot) {
        this.snapshot = snapshot;
    }
}
