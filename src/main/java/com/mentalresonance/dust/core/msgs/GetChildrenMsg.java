/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import com.mentalresonance.dust.core.actors.ActorRef;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 所有Actor都会通过提供其子级来响应此消息。这是由Actor内的内部消息处理处理的，
 * 因此无法在createBehavior()中重写
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class GetChildrenMsg implements Serializable
{
   /**
    * 返回的子级列表
    */
    List<ActorRef> children;
    /**
     * 用于区分请求和响应
     */
    Boolean isResponse = false;
    /**
     * 构造函数
     */
    public GetChildrenMsg() {}
}