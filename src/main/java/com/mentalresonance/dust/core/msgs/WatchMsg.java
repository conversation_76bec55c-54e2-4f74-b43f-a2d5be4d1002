/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import java.io.Serializable;

/**
 * 监视消息：将发送者添加到接收者的监视列表中
 *
 * 这是Actor监视机制的核心消息，用于建立Actor之间的监视关系。
 * 当接收到此消息的Actor停止时，会向所有监视者发送Terminated消息。
 *
 * 使用场景：
 * 1. 父Actor监视子Actor的生命周期
 * 2. 服务Actor监视依赖的其他服务
 * 3. 实现Actor的依赖管理
 * 4. 构建容错和自愈系统
 *
 * 监视机制特点：
 * - 单向监视：只有监视者会收到通知
 * - 自动清理：被监视者停止时自动移除监视关系
 * - 异步通知：通过消息传递进行通知
 * - 位置透明：支持本地和远程Actor监视
 *
 * <AUTHOR>
 */
public class WatchMsg implements Serializable {
    /**
     * 默认构造函数：创建监视消息实例
     *
     * 监视消息不需要额外的参数，发送者信息由消息传递系统自动提供。
     */
    public WatchMsg() {}
}
