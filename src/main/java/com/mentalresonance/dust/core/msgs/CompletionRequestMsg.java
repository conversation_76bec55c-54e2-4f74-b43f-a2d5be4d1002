/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import com.mentalresonance.dust.core.actors.ActorRef;
import com.mentalresonance.dust.core.actors.lib.CompletionServiceActor;
import lombok.Getter;

import java.io.Serializable;
import java.util.concurrent.CompletableFuture;

/**
 * 将消息的响应转换为已完成的Future值的消息。这允许我们连接Actor世界和Controller世界。
 * 如果超时，则异常完成。
 *
 * 此消息发送到{@link CompletionServiceActor}，它处理向目标发送消息和完成操作。
 *
 * @param <Response> Future响应类型
 *
 * <AUTHOR>
 */
@Getter
public class CompletionRequestMsg<Response> extends ProxyMsg {

    /**
     * 要完成的future。完成请求的目标引用*可能*是远程的，因此此消息需要可序列化，
     * 但CompletableFuture不可序列化，所以将其标记为transient - 它不会被发送到远程引用。
     * 这没有问题，因为实际完成操作的是CompletionActor，而它始终是本地的。
     */
    final transient CompletableFuture<Response> future;

    /**
     * 可选消息，代替此消息发送给目标
     */
    final Serializable passThroughMsg;

    /**
     * 消息被发送到目标。收到响应后（预期为Response类型），提供的future被完成。
     * @param target - 代理消息的目标
     * @param future - 用户提供的要设置的Future
     */
    public CompletionRequestMsg(ActorRef target, CompletableFuture<Response> future) {
        super(null);

        this.target = target;
        this.future = future;
        this.passThroughMsg = null;
    }

    /**
     * 消息被发送到目标。收到响应后（预期为Response类型），提供的future被完成。
     * @param target - 代理消息的目标
     * @param future - 用户提供的要设置的Future
     * @param passThroughMsg - 如果不为null，则发送passThroughMsg给目标而不是发送本消息。
     */
    public CompletionRequestMsg(ActorRef target, CompletableFuture<Response> future, Serializable passThroughMsg) {
        super(null);

        this.target = target;
        this.future = future;
        this.passThroughMsg = passThroughMsg;
    }
}