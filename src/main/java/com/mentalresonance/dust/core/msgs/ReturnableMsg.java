/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import com.mentalresonance.dust.core.actors.ActorRef;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * ReturnableMsg总是知道是谁发送的，因此接收Actor不必跟踪它。在管道中有用。
 *
 * <AUTHOR>
 */
public class ReturnableMsg implements Serializable {
    /**
     * 消息的发送者
     */
    @Setter
    @Getter
    ActorRef sender;

    /**
     * 用于确定消息去向的有用标志
     */
    protected boolean returning = false;

    /**
     * 构造函数
     * @param sender 此消息的发送者
     */
    public ReturnableMsg(ActorRef sender) { this.sender = sender; }

    /**
     * 回复者可以设置此标志（如果他们愿意）
     */
    public void setReturning() { returning = true; }

    /**
     * 方向
     * @return 如果在返回途中则为true
     */
    public boolean isReturning() { return returning; }
}