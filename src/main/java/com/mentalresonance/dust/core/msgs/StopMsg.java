/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import lombok.Getter;

import java.io.Serializable;

/**
 * 停止消息：通用的停止信号消息
 *
 * 这是一个便利的消息类型，具体含义由接收者决定。
 * 通常用于通知Actor停止执行某些操作或进入停止状态。
 *
 * 使用场景：
 * 1. 通知Actor优雅地停止处理
 * 2. 停止周期性任务或定时器
 * 3. 关闭资源连接
 * 4. 触发清理操作
 *
 * <AUTHOR>
 */
@Getter
public class StopMsg implements Serializable {

    /**
     * 可选的附加消息：可以携带额外的停止参数或原因信息
     */
    final Serializable msg;

    /**
     * 默认构造函数：创建不带附加消息的停止消息
     */
    public StopMsg() { msg = null; }

    /**
     * 带参数的构造函数：创建携带附加消息的停止消息
     *
     * @param msg 包装的附加消息，通常包含停止原因或相关参数
     */
    public StopMsg(Serializable msg) { this.msg = msg; }
}
