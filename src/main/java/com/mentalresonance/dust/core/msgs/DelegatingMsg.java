/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import lombok.Getter;

import java.io.Serializable;

/**
 * 委托消息：可由被委托的Actor发送给其父Actor，用于停止自身和委托关系，
 * 并让父Actor的新行为接收封装的消息
 *
 * 这是Actor委托模式的核心消息，用于实现行为的动态切换和委托关系的管理。
 *
 * 使用场景：
 * 1. 临时行为委托：Actor临时委托某些行为给子Actor处理
 * 2. 状态机转换：在不同状态间切换时使用委托
 * 3. 复杂业务逻辑分解：将复杂逻辑委托给专门的Actor
 * 4. 动态行为切换：根据运行时条件切换处理行为
 *
 * 工作流程：
 * 1. 父Actor创建子Actor并委托某些消息处理
 * 2. 子Actor处理完成后发送DelegatingMsg
 * 3. 父Actor停止子Actor和委托关系
 * 4. 父Actor恢复原有行为并处理封装的消息
 */
public class DelegatingMsg implements Serializable {

    /**
     * 可选消息：要发送给委托Actor的消息
     *
     * 这个消息将在委托关系结束后由父Actor的新行为处理
     */
    @Getter
    final Serializable msg;

    /**
     * 默认构造函数：创建不带消息的委托消息
     */
    public DelegatingMsg() {
        this.msg = null;
    }

    /**
     * 带参数构造函数：创建包含消息的委托消息
     *
     * @param msg 要发送给委托Actor的消息
     */
    public DelegatingMsg(Serializable msg) {
        this.msg = msg;
    }
}
