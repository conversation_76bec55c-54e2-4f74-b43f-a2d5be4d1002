/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import java.io.Serializable;
/**
 * Ping消息：用于测试连接和健康检查的消息
 *
 * 这是一个简单的信号消息，通常用于：
 * 1. 测试Actor是否存活和响应
 * 2. 网络连接的健康检查
 * 3. 触发某些周期性操作
 * 4. 作为心跳信号使用
 *
 * 具体含义由接收者决定。
 *
 * <AUTHOR>
 */
public class PingMsg implements Serializable {

    /**
     * 默认构造函数：创建Ping消息实例
     */
    public PingMsg() {}
}
