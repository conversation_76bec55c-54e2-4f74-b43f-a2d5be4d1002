/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import com.mentalresonance.dust.core.actors.PersistentActor;

import java.io.Serializable;

/**
 * 如果删除快照的尝试成功，则发送给{@link PersistentActor}
 *
 *  <AUTHOR>
 */
public class DeleteSnapshotSuccessMsg implements Serializable {
    /**
     * 构造函数
     */
    public DeleteSnapshotSuccessMsg() {}
}