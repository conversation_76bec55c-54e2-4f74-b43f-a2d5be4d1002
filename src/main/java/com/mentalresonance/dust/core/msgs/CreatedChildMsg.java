/*
 *
 *  Copyright 2024-Present <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  https://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *
 */

package com.mentalresonance.dust.core.msgs;

import lombok.Getter;

import java.io.Serializable;

/**
 * CreateChildMsg成功响应
 *
 *  <AUTHOR>
 */
@Getter
public class CreatedChildMsg implements Serializable {
    /**
     * 新创建子级的名称
     */
    final String name;

    /**
     * 构造函数
     * @param name 已创建子级的名称
     */
    public CreatedChildMsg(String name) {
        this.name = name;
    }
}