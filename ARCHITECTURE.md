# Dust-Core 架构设计文档

## 项目概述

Dust-Core是一个基于Actor模型的高性能分布式系统框架，采用Java 21开发。该框架提供了轻量级的Actor实现，支持本地和远程通信，具有出色的性能和可扩展性。

## 核心架构

### 1. Actor模型核心组件

#### 1.1 Actor类层次结构
```
Actor (基类)
├── PersistentActor (持久化Actor)
├── GuardianActor (守护Actor)
├── SystemActor (系统Actor)
├── UserActor (用户Actor)
└── 各种库Actor (LogActor, ReaperActor等)
```

#### 1.2 ActorSystem (Actor系统)
- **职责**: 整个Actor框架的核心容器
- **功能**:
  - 管理Actor的生命周期和层次结构
  - 提供Actor之间的通信基础设施
  - 支持本地和远程Actor的透明通信
  - 管理系统级服务（持久化、序列化等）
  - 处理Actor的监督和错误恢复

#### 1.3 ActorRef (Actor引用)
- **职责**: Actor的代理和通信接口
- **特性**:
  - 位置透明：无论Actor在本地还是远程
  - 线程安全的消息发送
  - 生命周期管理
  - 监视和取消监视功能

### 2. 消息传递系统

#### 2.1 消息类型
- **系统消息**: 生命周期管理、监督策略相关
- **用户消息**: 业务逻辑相关的自定义消息
- **控制消息**: StartMsg, StopMsg, PingMsg等

#### 2.2 邮箱机制
- **DustLinkedBlockingQueue**: 高性能的消息队列
- **死信处理**: 无法投递的消息的处理机制
- **消息暂存**: 支持消息的临时存储和批量处理

### 3. 持久化系统

#### 3.1 多种持久化实现
- **JacksonPersistenceService**: 基于Jackson的JSON持久化
- **GsonPersistenceService**: 基于Gson的JSON持久化
- **FSTPersistenceService**: 基于FST的二进制持久化

#### 3.2 快照机制
- 支持Actor状态的快照保存和恢复
- 异步持久化，不阻塞Actor处理
- 可配置的持久化策略

### 4. 序列化系统

#### 4.1 SerializationService
- **FST序列化**: 高性能二进制序列化
- **JSON序列化**: 支持调试和外部API
- **类注册优化**: 预注册类以提高性能

### 5. 网络通信

#### 5.1 远程Actor支持
- **CoreTCPObjectServer**: TCP对象服务器
- **ActorSystemConnectionManager**: 连接池管理
- **位置透明**: 本地和远程Actor使用相同的API

## 设计优势

### 1. 高性能设计
- **虚拟线程**: 利用Java 21的虚拟线程特性
- **无锁设计**: 最小化锁的使用，提高并发性能
- **高效序列化**: FST序列化比标准Java序列化快数倍
- **连接池**: 复用网络连接，减少开销

### 2. 容错性
- **监督策略**: 灵活的错误处理和恢复机制
- **Actor隔离**: 单个Actor的失败不影响其他Actor
- **自动重启**: 支持Actor的自动重启和状态恢复

### 3. 可扩展性
- **层次结构**: 清晰的Actor层次结构便于管理
- **插件化**: 支持自定义持久化和序列化实现
- **远程通信**: 支持分布式部署

### 4. 易用性
- **简洁API**: 直观的消息传递API
- **丰富的库Actor**: 提供常用的Actor实现
- **完善的日志**: 详细的调试和监控信息

## 关键设计模式

### 1. Actor模式
- 封装状态和行为
- 通过消息传递进行通信
- 单线程处理保证线程安全

### 2. 监督者模式
- 父Actor监督子Actor
- 分层的错误处理策略
- 故障隔离和恢复

### 3. 发布-订阅模式
- 支持消息的发布和订阅
- 解耦消息生产者和消费者

### 4. 命令模式
- 消息作为命令对象
- 支持消息的序列化和持久化

## 性能特性

### 1. 内存效率
- 轻量级Actor实现
- 高效的消息队列
- 可配置的缓存策略

### 2. 处理能力
- 支持数百万个Actor
- 高吞吐量的消息处理
- 低延迟的消息传递

### 3. 网络性能
- 高效的序列化协议
- 连接复用和池化
- 异步网络IO

## 使用场景

### 1. 微服务架构
- 服务间的异步通信
- 分布式状态管理
- 服务的自动恢复

### 2. 实时系统
- 游戏服务器
- 聊天系统
- 实时数据处理

### 3. 大数据处理
- 流式数据处理
- 分布式计算
- 数据管道构建

### 4. IoT系统
- 设备状态管理
- 消息路由和处理
- 边缘计算

## 配置和部署

### 1. 系统配置
- 支持配置文件和环境变量
- 可配置的持久化目录
- 灵活的网络配置

### 2. 监控和调试
- 详细的日志记录
- 性能指标收集
- 调试工具支持

### 3. 集群部署
- 支持多节点部署
- 自动服务发现
- 负载均衡

## 总结

Dust-Core框架通过精心设计的架构，提供了一个高性能、可靠、易用的Actor模型实现。其模块化的设计使得框架既适合小型应用，也能支撑大规模分布式系统的需求。通过合理的抽象和优化，框架在保持简洁API的同时，提供了强大的功能和出色的性能。
