# Dust-Core 快速入门指南

## 简介

Dust-Core是一个基于Java 21虚拟线程的高性能Actor框架，提供了简单易用的API来构建并发和分布式应用程序。

## 5分钟快速开始

### 1. 创建第一个Actor

```java
import com.mentalresonance.dust.core.actors.Actor;
import com.mentalresonance.dust.core.actors.ActorBehavior;
import com.mentalresonance.dust.core.actors.Props;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HelloActor extends Actor {
    
    private String name;
    
    // 创建Props的静态方法
    public static Props props(String name) {
        return Props.create(HelloActor.class, name);
    }
    
    // 构造函数
    public HelloActor(String name) {
        this.name = name;
    }
    
    // 定义Actor的行为
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                case String msg -> {
                    log.info("{} 收到消息: {}", name, msg);
                    // 回复发送者
                    sender.tell("你好！我是 " + name, self);
                }
                case Integer number -> {
                    log.info("{} 收到数字: {}", name, number);
                    // 计算并回复
                    sender.tell(number * 2, self);
                }
                default -> {
                    log.warn("{} 收到未知消息类型: {}", name, message.getClass());
                }
            }
        };
    }
}
```

### 2. 创建Actor系统并使用Actor

```java
import com.mentalresonance.dust.core.actors.ActorSystem;
import com.mentalresonance.dust.core.actors.ActorRef;

public class QuickStartExample {
    
    public static void main(String[] args) throws Exception {
        // 创建Actor系统
        ActorSystem system = ActorSystem.create("QuickStartSystem");
        
        // 创建Actor
        ActorRef helloActor = system.getContext().actorOf(
            HelloActor.props("HelloWorld"), "hello-actor");
        
        // 发送消息
        helloActor.tell("Hello, Dust-Core!", null);
        helloActor.tell(42, null);
        
        // 等待一下让消息处理完成
        Thread.sleep(1000);
        
        // 关闭系统
        system.stop();
    }
}
```

### 3. 运行结果

```
[INFO] HelloWorld 收到消息: Hello, Dust-Core!
[INFO] HelloWorld 收到数字: 42
```

## 核心概念

### Actor
- **定义**: Actor是系统中的基本计算单元，拥有自己的状态和行为
- **特点**: 通过消息通信，不共享状态，天然线程安全
- **生命周期**: preStart() → 消息处理 → postStop()

### 消息传递
```java
// 发送消息（fire-and-forget）
actorRef.tell(message, sender);

// 发送消息给自己
tellSelf(message);

// 延迟发送消息
scheduleIn(message, 5000L); // 5秒后发送
```

### Actor层次结构
```
ActorSystem
├── /user (用户Actor的根)
│   ├── /user/my-actor
│   └── /user/another-actor
└── /system (系统Actor)
    ├── /system/deadletters
    └── /system/log
```

## 常用模式

### 1. 请求-响应模式

```java
// 发送消息并等待响应
public class RequestResponseExample {
    
    public static class RequestMsg implements Serializable {
        public final String data;
        public RequestMsg(String data) { this.data = data; }
    }
    
    public static class ResponseMsg implements Serializable {
        public final String result;
        public ResponseMsg(String result) { this.result = result; }
    }
    
    // 在Actor中处理
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                case RequestMsg req -> {
                    // 处理请求
                    String result = processRequest(req.data);
                    // 回复响应
                    sender.tell(new ResponseMsg(result), self);
                }
            }
        };
    }
}
```

### 2. 状态管理

```java
@Slf4j
public class CounterActor extends Actor {
    
    private int count = 0;
    
    public static Props props() {
        return Props.create(CounterActor.class);
    }
    
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                case String cmd when "increment".equals(cmd) -> {
                    count++;
                    log.info("计数器增加到: {}", count);
                    sender.tell(count, self);
                }
                case String cmd when "get".equals(cmd) -> {
                    sender.tell(count, self);
                }
                case String cmd when "reset".equals(cmd) -> {
                    count = 0;
                    log.info("计数器已重置");
                    sender.tell(count, self);
                }
            }
        };
    }
}
```

### 3. 子Actor管理

```java
@Slf4j
public class ParentActor extends Actor {
    
    private ActorRef childActor;
    
    public static Props props() {
        return Props.create(ParentActor.class);
    }
    
    @Override
    protected void preStart() {
        // 创建子Actor
        try {
            childActor = actorOf(HelloActor.props("Child"), "child-actor");
            log.info("子Actor已创建: {}", childActor.path);
        } catch (Exception e) {
            log.error("创建子Actor失败", e);
        }
    }
    
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                case String msg when msg.startsWith("forward:") -> {
                    // 转发消息给子Actor
                    String actualMsg = msg.substring(8);
                    childActor.tell(actualMsg, sender);
                }
                default -> {
                    log.info("父Actor收到消息: {}", message);
                }
            }
        };
    }
}
```

## 持久化Actor

```java
@Slf4j
public class PersistentCounterActor extends PersistentActor {
    
    @Data
    public static class CounterState implements Serializable {
        private int count = 0;
    }
    
    private CounterState state = new CounterState();
    
    public static Props props() {
        return Props.create(PersistentCounterActor.class);
    }
    
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                case String cmd when "increment".equals(cmd) -> {
                    state.setCount(state.getCount() + 1);
                    saveSnapshot(state); // 保存状态
                    log.info("持久化计数器: {}", state.getCount());
                    sender.tell(state.getCount(), self);
                }
                case String cmd when "get".equals(cmd) -> {
                    sender.tell(state.getCount(), self);
                }
            }
        };
    }
    
    @Override
    protected void onRecovery(Serializable recoveredState) {
        if (recoveredState instanceof CounterState counterState) {
            this.state = counterState;
            log.info("状态已恢复，计数器: {}", state.getCount());
        }
    }
}
```

## 错误处理

```java
@Slf4j
public class RobustActor extends Actor {
    
    public static Props props() {
        return Props.create(RobustActor.class);
    }
    
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            try {
                switch (message) {
                    case String msg -> {
                        if ("error".equals(msg)) {
                            throw new RuntimeException("模拟错误");
                        }
                        log.info("处理消息: {}", msg);
                    }
                }
            } catch (Exception e) {
                log.error("处理消息时发生错误: {}", e.getMessage());
                // 错误会被监督策略处理
                throw e;
            }
        };
    }
    
    @Override
    protected void onResume() {
        log.info("Actor已恢复");
    }
    
    @Override
    protected void preRestart(Throwable reason) {
        log.info("Actor即将重启，原因: {}", reason.getMessage());
    }
}
```

## 最佳实践

### 1. 消息设计
- 使用不可变对象作为消息
- 实现Serializable接口以支持远程通信
- 使用有意义的消息类名

### 2. Actor设计
- 保持Actor的职责单一
- 避免在Actor中进行阻塞操作
- 合理使用子Actor来分解复杂逻辑

### 3. 错误处理
- 设计合适的监督策略
- 使用持久化保存重要状态
- 实现优雅的错误恢复机制

### 4. 性能优化
- 避免创建过多的小消息
- 合理使用消息批处理
- 监控Actor的邮箱大小

## 下一步

- 查看完整的聊天系统案例 (DUST_CORE_EXAMPLE.md)
- 了解分布式Actor通信
- 学习高级监督策略
- 探索集群部署方案

通过这个快速入门指南，您已经掌握了Dust-Core的基本用法。这个框架的强大之处在于其简单性和可扩展性，让您能够轻松构建高并发、容错的分布式应用程序。
