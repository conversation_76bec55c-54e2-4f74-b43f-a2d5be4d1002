# Dust-Core Actor框架完整使用案例

## 项目概述

这个案例展示了如何使用Dust-Core框架构建一个分布式的在线聊天系统，包含用户管理、聊天室管理、消息路由等功能。通过这个案例，您将学会：

1. Actor系统的创建和配置
2. 不同类型Actor的实现
3. 消息传递和路由
4. 持久化状态管理
5. 监督策略和错误处理
6. 远程Actor通信

## 系统架构

```
ActorSystem
├── /user
│   ├── ChatRoomManager (聊天室管理器)
│   ├── UserManager (用户管理器)
│   └── MessageRouter (消息路由器)
└── /system
    ├── deadletters
    └── log
```

## 1. 消息定义

首先定义系统中使用的各种消息类型：

```java
// 用户相关消息
public class UserJoinMsg implements Serializable {
    public final String userId;
    public final String username;
    
    public UserJoinMsg(String userId, String username) {
        this.userId = userId;
        this.username = username;
    }
}

public class UserLeaveMsg implements Serializable {
    public final String userId;
    
    public UserLeaveMsg(String userId) {
        this.userId = userId;
    }
}

// 聊天室相关消息
public class CreateRoomMsg implements Serializable {
    public final String roomId;
    public final String roomName;
    public final String creatorId;
    
    public CreateRoomMsg(String roomId, String roomName, String creatorId) {
        this.roomId = roomId;
        this.roomName = roomName;
        this.creatorId = creatorId;
    }
}

public class JoinRoomMsg implements Serializable {
    public final String userId;
    public final String roomId;
    
    public JoinRoomMsg(String userId, String roomId) {
        this.userId = userId;
        this.roomId = roomId;
    }
}

// 消息相关
public class ChatMessage implements Serializable {
    public final String fromUserId;
    public final String toRoomId;
    public final String content;
    public final long timestamp;
    
    public ChatMessage(String fromUserId, String toRoomId, String content) {
        this.fromUserId = fromUserId;
        this.toRoomId = toRoomId;
        this.content = content;
        this.timestamp = System.currentTimeMillis();
    }
}

public class BroadcastMsg implements Serializable {
    public final ChatMessage message;
    public final List<String> targetUsers;
    
    public BroadcastMsg(ChatMessage message, List<String> targetUsers) {
        this.message = message;
        this.targetUsers = targetUsers;
    }
}
```

## 2. 用户Actor实现

```java
@Slf4j
public class UserActor extends PersistentActor {
    
    // 用户状态
    @Data
    public static class UserState implements Serializable {
        private String userId;
        private String username;
        private Set<String> joinedRooms = new HashSet<>();
        private boolean online = false;
    }
    
    private UserState state;
    private final String userId;
    
    public static Props props(String userId) {
        return Props.create(UserActor.class, userId);
    }
    
    public UserActor(String userId) {
        this.userId = userId;
        this.state = new UserState();
        this.state.setUserId(userId);
    }
    
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                case UserJoinMsg msg -> {
                    state.setUsername(msg.username);
                    state.setOnline(true);
                    saveSnapshot(state);
                    log.info("用户 {} ({}) 已上线", msg.username, msg.userId);
                    sender.tell(new StatusMsg(true, "用户上线成功"), self);
                }
                
                case JoinRoomMsg msg -> {
                    state.getJoinedRooms().add(msg.roomId);
                    saveSnapshot(state);
                    log.info("用户 {} 加入聊天室 {}", state.getUsername(), msg.roomId);
                    
                    // 通知聊天室管理器
                    ActorRef roomManager = actorSelection("/user/ChatRoomManager").getRef();
                    roomManager.tell(msg, self);
                }
                
                case ChatMessage msg -> {
                    log.info("用户 {} 收到消息: {}", state.getUsername(), msg.content);
                    // 这里可以添加消息处理逻辑，比如推送到客户端
                }
                
                case UserLeaveMsg msg -> {
                    state.setOnline(false);
                    state.getJoinedRooms().clear();
                    saveSnapshot(state);
                    log.info("用户 {} 已下线", state.getUsername());
                }
                
                default -> log.warn("UserActor收到未知消息: {}", message);
            }
        };
    }
    
    @Override
    protected void onRecovery(Serializable state) {
        if (state instanceof UserState userState) {
            this.state = userState;
            log.info("用户Actor {} 状态已恢复", userId);
        }
    }
}
```

## 3. 聊天室Actor实现

```java
@Slf4j
public class ChatRoomActor extends PersistentActor {
    
    @Data
    public static class RoomState implements Serializable {
        private String roomId;
        private String roomName;
        private String creatorId;
        private Set<String> members = new HashSet<>();
        private List<ChatMessage> messageHistory = new ArrayList<>();
    }
    
    private RoomState state;
    private final String roomId;
    
    public static Props props(String roomId) {
        return Props.create(ChatRoomActor.class, roomId);
    }
    
    public ChatRoomActor(String roomId) {
        this.roomId = roomId;
        this.state = new RoomState();
        this.state.setRoomId(roomId);
    }
    
    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                case CreateRoomMsg msg -> {
                    state.setRoomName(msg.roomName);
                    state.setCreatorId(msg.creatorId);
                    state.getMembers().add(msg.creatorId);
                    saveSnapshot(state);
                    log.info("聊天室 {} 已创建，创建者: {}", msg.roomName, msg.creatorId);
                    sender.tell(new StatusMsg(true, "聊天室创建成功"), self);
                }
                
                case JoinRoomMsg msg -> {
                    state.getMembers().add(msg.userId);
                    saveSnapshot(state);
                    log.info("用户 {} 加入聊天室 {}", msg.userId, state.getRoomName());
                    
                    // 通知所有成员
                    broadcastToMembers(new ChatMessage("system", roomId, 
                        "用户 " + msg.userId + " 加入了聊天室"));
                }
                
                case ChatMessage msg -> {
                    // 保存消息历史
                    state.getMessageHistory().add(msg);
                    if (state.getMessageHistory().size() > 100) {
                        state.getMessageHistory().remove(0); // 保持最近100条消息
                    }
                    saveSnapshot(state);
                    
                    log.info("聊天室 {} 收到消息: {} -> {}", 
                        state.getRoomName(), msg.fromUserId, msg.content);
                    
                    // 广播给所有成员
                    broadcastToMembers(msg);
                }
                
                default -> log.warn("ChatRoomActor收到未知消息: {}", message);
            }
        };
    }
    
    private void broadcastToMembers(ChatMessage message) {
        ActorRef messageRouter = actorSelection("/user/MessageRouter").getRef();
        messageRouter.tell(new BroadcastMsg(message, 
            new ArrayList<>(state.getMembers())), self);
    }
    
    @Override
    protected void onRecovery(Serializable state) {
        if (state instanceof RoomState roomState) {
            this.state = roomState;
            log.info("聊天室Actor {} 状态已恢复", roomId);
        }
    }
}

## 4. 管理器Actor实现

### 用户管理器
```java
@Slf4j
public class UserManagerActor extends Actor {

    private final Map<String, ActorRef> userActors = new ConcurrentHashMap<>();

    public static Props props() {
        return Props.create(UserManagerActor.class);
    }

    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                case UserJoinMsg msg -> {
                    ActorRef userActor = userActors.computeIfAbsent(msg.userId,
                        userId -> {
                            try {
                                return actorOf(UserActor.props(userId), "user-" + userId);
                            } catch (ActorInstantiationException e) {
                                log.error("创建用户Actor失败: {}", e.getMessage());
                                return null;
                            }
                        });

                    if (userActor != null) {
                        userActor.tell(msg, sender);
                    }
                }

                case UserLeaveMsg msg -> {
                    ActorRef userActor = userActors.get(msg.userId);
                    if (userActor != null) {
                        userActor.tell(msg, sender);
                        // 可以选择是否立即移除Actor或延迟移除
                        scheduleIn(new CleanupUserMsg(msg.userId), 60000L); // 1分钟后清理
                    }
                }

                case CleanupUserMsg msg -> {
                    ActorRef userActor = userActors.remove(msg.userId);
                    if (userActor != null) {
                        context.stop(userActor);
                        log.info("清理用户Actor: {}", msg.userId);
                    }
                }

                default -> log.warn("UserManagerActor收到未知消息: {}", message);
            }
        };
    }

    // 清理消息
    public static class CleanupUserMsg implements Serializable {
        public final String userId;
        public CleanupUserMsg(String userId) { this.userId = userId; }
    }
}
```

### 聊天室管理器
```java
@Slf4j
public class ChatRoomManagerActor extends Actor {

    private final Map<String, ActorRef> roomActors = new ConcurrentHashMap<>();

    public static Props props() {
        return Props.create(ChatRoomManagerActor.class);
    }

    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                case CreateRoomMsg msg -> {
                    ActorRef roomActor = roomActors.computeIfAbsent(msg.roomId,
                        roomId -> {
                            try {
                                return actorOf(ChatRoomActor.props(roomId), "room-" + roomId);
                            } catch (ActorInstantiationException e) {
                                log.error("创建聊天室Actor失败: {}", e.getMessage());
                                return null;
                            }
                        });

                    if (roomActor != null) {
                        roomActor.tell(msg, sender);
                    }
                }

                case JoinRoomMsg msg -> {
                    ActorRef roomActor = roomActors.get(msg.roomId);
                    if (roomActor != null) {
                        roomActor.tell(msg, sender);
                    } else {
                        sender.tell(new StatusMsg(false, "聊天室不存在"), self);
                    }
                }

                case ChatMessage msg -> {
                    ActorRef roomActor = roomActors.get(msg.toRoomId);
                    if (roomActor != null) {
                        roomActor.tell(msg, sender);
                    } else {
                        log.warn("消息发送失败，聊天室不存在: {}", msg.toRoomId);
                    }
                }

                default -> log.warn("ChatRoomManagerActor收到未知消息: {}", message);
            }
        };
    }
}
```

### 消息路由器
```java
@Slf4j
public class MessageRouterActor extends Actor {

    public static Props props() {
        return Props.create(MessageRouterActor.class);
    }

    @Override
    protected ActorBehavior createBehavior() {
        return message -> {
            switch (message) {
                case BroadcastMsg msg -> {
                    ActorRef userManager = actorSelection("/user/UserManager").getRef();

                    // 向每个目标用户发送消息
                    for (String userId : msg.targetUsers) {
                        if (!userId.equals(msg.message.fromUserId)) { // 不发送给自己
                            try {
                                ActorRef userActor = actorSelection("/user/UserManager/user-" + userId).getRef();
                                userActor.tell(msg.message, self);
                            } catch (Exception e) {
                                log.warn("发送消息给用户 {} 失败: {}", userId, e.getMessage());
                            }
                        }
                    }
                }

                case ChatMessage msg -> {
                    // 直接路由消息到聊天室
                    ActorRef roomManager = actorSelection("/user/ChatRoomManager").getRef();
                    roomManager.tell(msg, sender);
                }

                default -> log.warn("MessageRouterActor收到未知消息: {}", message);
            }
        };
    }
}
```

## 5. 主应用程序

```java
@Slf4j
public class ChatApplication {

    public static void main(String[] args) {
        try {
            // 创建Actor系统
            ActorSystem system = ActorSystem.create("ChatSystem", 8080);

            // 设置持久化服务
            system.getContext().setPersistenceService(
                GsonPersistenceService.create("./chat-data", "json"));

            // 创建管理器Actor
            ActorRef userManager = system.getContext().actorOf(
                UserManagerActor.props(), "UserManager");
            ActorRef roomManager = system.getContext().actorOf(
                ChatRoomManagerActor.props(), "ChatRoomManager");
            ActorRef messageRouter = system.getContext().actorOf(
                MessageRouterActor.props(), "MessageRouter");

            log.info("聊天系统已启动，端口: 8080");

            // 模拟一些操作
            simulateChatOperations(system);

            // 保持系统运行
            Thread.sleep(30000);

            // 优雅关闭
            system.stop();

        } catch (Exception e) {
            log.error("启动聊天系统失败", e);
        }
    }

    private static void simulateChatOperations(ActorSystem system) {
        try {
            ActorRef userManager = system.getContext().actorSelection("/user/UserManager");
            ActorRef roomManager = system.getContext().actorSelection("/user/ChatRoomManager");
            ActorRef messageRouter = system.getContext().actorSelection("/user/MessageRouter");

            // 用户上线
            userManager.tell(new UserJoinMsg("user1", "Alice"), null);
            userManager.tell(new UserJoinMsg("user2", "Bob"), null);
            userManager.tell(new UserJoinMsg("user3", "Charlie"), null);

            Thread.sleep(1000);

            // 创建聊天室
            roomManager.tell(new CreateRoomMsg("room1", "技术讨论", "user1"), null);

            Thread.sleep(1000);

            // 用户加入聊天室
            userManager.tell(new JoinRoomMsg("user1", "room1"), null);
            userManager.tell(new JoinRoomMsg("user2", "room1"), null);
            userManager.tell(new JoinRoomMsg("user3", "room1"), null);

            Thread.sleep(1000);

            // 发送消息
            messageRouter.tell(new ChatMessage("user1", "room1", "大家好！"), null);
            messageRouter.tell(new ChatMessage("user2", "room1", "你好Alice！"), null);
            messageRouter.tell(new ChatMessage("user3", "room1", "很高兴见到大家"), null);

        } catch (Exception e) {
            log.error("模拟操作失败", e);
        }
    }
}
```

## 6. 监督策略配置

```java
public class ChatSupervisorStrategy extends SupervisionStrategy {

    public ChatSupervisorStrategy() {
        super(SS_RESTART, MODE_ONE_FOR_ONE);
    }

    @Override
    protected SupervisionStrategy strategy(ActorRef ref, Throwable thrown) {
        // 根据异常类型决定策略
        if (thrown instanceof IllegalArgumentException) {
            return new SupervisionStrategy(SS_RESUME, MODE_ONE_FOR_ONE);
        } else if (thrown instanceof RuntimeException) {
            return new SupervisionStrategy(SS_RESTART, MODE_ONE_FOR_ONE);
        } else {
            return new SupervisionStrategy(SS_STOP, MODE_ONE_FOR_ONE);
        }
    }
}
```

## 7. 运行结果

当您运行这个应用程序时，您会看到类似以下的输出：

```
[INFO] 聊天系统已启动，端口: 8080
[INFO] 用户 Alice (user1) 已上线
[INFO] 用户 Bob (user2) 已上线
[INFO] 用户 Charlie (user3) 已上线
[INFO] 聊天室 技术讨论 已创建，创建者: user1
[INFO] 用户 user1 加入聊天室 room1
[INFO] 用户 user2 加入聊天室 room1
[INFO] 用户 user3 加入聊天室 room1
[INFO] 聊天室 技术讨论 收到消息: user1 -> 大家好！
[INFO] 用户 Bob 收到消息: 大家好！
[INFO] 用户 Charlie 收到消息: 大家好！
```

## 8. 扩展功能

这个案例可以进一步扩展：

1. **私聊功能**: 添加用户间的直接消息
2. **文件传输**: 支持文件和图片分享
3. **用户权限**: 实现管理员、普通用户等角色
4. **消息持久化**: 将聊天记录保存到数据库
5. **集群部署**: 使用多个ActorSystem实现分布式部署
6. **WebSocket集成**: 添加Web前端支持
7. **推送通知**: 实现离线消息推送

## 总结

这个案例展示了Dust-Core框架的强大功能：

- **Actor模型**: 每个组件都是独立的Actor，易于理解和维护
- **消息驱动**: 所有交互都通过消息进行，实现了松耦合
- **持久化**: 自动保存和恢复Actor状态
- **容错性**: 通过监督策略处理异常
- **可扩展性**: 易于添加新功能和扩展到分布式环境

通过这个案例，您可以看到Dust-Core如何简化复杂分布式系统的开发，让您专注于业务逻辑而不是底层的并发和网络处理。
```
