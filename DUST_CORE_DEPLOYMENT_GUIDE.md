# Dust-Core 部署和配置指南

## 项目依赖配置

### Maven配置 (pom.xml)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.example</groupId>
    <artifactId>dust-core-app</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    
    <dependencies>
        <!-- Dust-Core 核心依赖 -->
        <dependency>
            <groupId>com.mentalresonance</groupId>
            <artifactId>dust-core</artifactId>
            <version>1.0.0</version>
        </dependency>
        
        <!-- 日志框架 -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.4.11</version>
        </dependency>
        
        <!-- Lombok (可选) -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>
        
        <!-- JSON处理 -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>
        
        <!-- 或者使用Jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.2</version>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <compilerArgs>
                        <arg>--enable-preview</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            
            <!-- 打包可执行JAR -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.1.5</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
```

### Gradle配置 (build.gradle)

```gradle
plugins {
    id 'java'
    id 'application'
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
}

dependencies {
    // Dust-Core 核心依赖
    implementation 'com.mentalresonance:dust-core:1.0.0'
    
    // 日志框架
    implementation 'ch.qos.logback:logback-classic:1.4.11'
    
    // Lombok
    compileOnly 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'
    
    // JSON处理
    implementation 'com.google.code.gson:gson:2.10.1'
    // 或者 implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
}

application {
    mainClass = 'com.example.MyApplication'
}

tasks.withType(JavaCompile) {
    options.compilerArgs += ['--enable-preview']
}

tasks.withType(JavaExec) {
    jvmArgs += ['--enable-preview']
}
```

## 配置文件

### 日志配置 (logback.xml)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/dust-core-app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/dust-core-app.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- Actor系统日志级别 -->
    <logger name="com.mentalresonance.dust" level="INFO"/>
    
    <!-- 应用程序日志级别 -->
    <logger name="com.example" level="DEBUG"/>
    
    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
    
</configuration>
```

### 应用配置 (application.properties)

```properties
# Actor系统配置
dust.system.name=MyActorSystem
dust.system.port=8080
dust.system.host=0.0.0.0

# 持久化配置
dust.persistence.type=gson
dust.persistence.directory=./data/snapshots
dust.persistence.file-extension=json

# 网络配置
dust.network.connection-timeout=30000
dust.network.max-connections-per-host=16
dust.network.ping-interval=15000
dust.network.ping-timeout=30000

# JVM配置
dust.jvm.virtual-threads=true
dust.jvm.max-heap=2g
dust.jvm.gc=G1GC

# 监控配置
dust.monitoring.enabled=true
dust.monitoring.metrics-port=9090
dust.monitoring.health-check-interval=60000
```

## 单机部署

### 1. 基本部署脚本 (deploy.sh)

```bash
#!/bin/bash

# 设置变量
APP_NAME="dust-core-app"
APP_VERSION="1.0.0"
APP_JAR="${APP_NAME}-${APP_VERSION}.jar"
PID_FILE="/var/run/${APP_NAME}.pid"
LOG_DIR="/var/log/${APP_NAME}"
DATA_DIR="/var/lib/${APP_NAME}"

# 创建必要的目录
mkdir -p ${LOG_DIR}
mkdir -p ${DATA_DIR}

# JVM参数
JVM_OPTS="-Xmx2g -Xms1g"
JVM_OPTS="${JVM_OPTS} -XX:+UseG1GC"
JVM_OPTS="${JVM_OPTS} -XX:+UseStringDeduplication"
JVM_OPTS="${JVM_OPTS} -XX:MaxGCPauseMillis=200"
JVM_OPTS="${JVM_OPTS} --enable-preview"

# 应用参数
APP_OPTS="--spring.profiles.active=production"
APP_OPTS="${APP_OPTS} --logging.file.path=${LOG_DIR}"
APP_OPTS="${APP_OPTS} --dust.persistence.directory=${DATA_DIR}/snapshots"

# 启动函数
start() {
    if [ -f ${PID_FILE} ] && kill -0 $(cat ${PID_FILE}) 2>/dev/null; then
        echo "${APP_NAME} is already running"
        return 1
    fi
    
    echo "Starting ${APP_NAME}..."
    nohup java ${JVM_OPTS} -jar ${APP_JAR} ${APP_OPTS} > ${LOG_DIR}/startup.log 2>&1 &
    echo $! > ${PID_FILE}
    echo "${APP_NAME} started with PID $(cat ${PID_FILE})"
}

# 停止函数
stop() {
    if [ ! -f ${PID_FILE} ]; then
        echo "${APP_NAME} is not running"
        return 1
    fi
    
    PID=$(cat ${PID_FILE})
    echo "Stopping ${APP_NAME} (PID: ${PID})..."
    kill ${PID}
    
    # 等待进程结束
    for i in {1..30}; do
        if ! kill -0 ${PID} 2>/dev/null; then
            break
        fi
        sleep 1
    done
    
    # 强制杀死
    if kill -0 ${PID} 2>/dev/null; then
        echo "Force killing ${APP_NAME}..."
        kill -9 ${PID}
    fi
    
    rm -f ${PID_FILE}
    echo "${APP_NAME} stopped"
}

# 重启函数
restart() {
    stop
    sleep 2
    start
}

# 状态检查
status() {
    if [ -f ${PID_FILE} ] && kill -0 $(cat ${PID_FILE}) 2>/dev/null; then
        echo "${APP_NAME} is running (PID: $(cat ${PID_FILE}))"
    else
        echo "${APP_NAME} is not running"
    fi
}

# 主逻辑
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac
```

### 2. Systemd服务配置 (dust-core-app.service)

```ini
[Unit]
Description=Dust-Core Application
After=network.target

[Service]
Type=simple
User=dustcore
Group=dustcore
WorkingDirectory=/opt/dust-core-app
ExecStart=/usr/bin/java -Xmx2g -Xms1g -XX:+UseG1GC --enable-preview -jar dust-core-app-1.0.0.jar
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=dust-core-app

# 环境变量
Environment=JAVA_HOME=/usr/lib/jvm/java-21-openjdk
Environment=DUST_DATA_DIR=/var/lib/dust-core-app
Environment=DUST_LOG_DIR=/var/log/dust-core-app

[Install]
WantedBy=multi-user.target
```

## 分布式部署

### 1. 集群配置

```java
// 节点1配置 (node1.properties)
dust.system.name=ClusterSystem
dust.system.port=8080
dust.system.host=************
dust.cluster.seeds=************:8080,************:8080,************:8080
dust.cluster.node-id=node1

// 节点2配置 (node2.properties)  
dust.system.name=ClusterSystem
dust.system.port=8080
dust.system.host=************
dust.cluster.seeds=************:8080,************:8080,************:8080
dust.cluster.node-id=node2

// 节点3配置 (node3.properties)
dust.system.name=ClusterSystem
dust.system.port=8080
dust.system.host=************
dust.cluster.seeds=************:8080,************:8080,************:8080
dust.cluster.node-id=node3
```

### 2. Docker部署

```dockerfile
# Dockerfile
FROM openjdk:21-jdk-slim

# 设置工作目录
WORKDIR /app

# 复制应用文件
COPY target/dust-core-app-1.0.0.jar app.jar
COPY src/main/resources/logback.xml logback.xml

# 创建数据目录
RUN mkdir -p /app/data /app/logs

# 设置环境变量
ENV JAVA_OPTS="-Xmx1g -Xms512m -XX:+UseG1GC --enable-preview"
ENV DUST_DATA_DIR="/app/data"
ENV DUST_LOG_DIR="/app/logs"

# 暴露端口
EXPOSE 8080 9090

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:9090/health || exit 1

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  dust-node1:
    build: .
    container_name: dust-node1
    hostname: dust-node1
    ports:
      - "8080:8080"
      - "9090:9090"
    environment:
      - DUST_SYSTEM_HOST=dust-node1
      - DUST_SYSTEM_PORT=8080
      - DUST_CLUSTER_SEEDS=dust-node1:8080,dust-node2:8080,dust-node3:8080
      - DUST_NODE_ID=node1
    volumes:
      - dust-node1-data:/app/data
      - dust-node1-logs:/app/logs
    networks:
      - dust-cluster

  dust-node2:
    build: .
    container_name: dust-node2
    hostname: dust-node2
    ports:
      - "8081:8080"
      - "9091:9090"
    environment:
      - DUST_SYSTEM_HOST=dust-node2
      - DUST_SYSTEM_PORT=8080
      - DUST_CLUSTER_SEEDS=dust-node1:8080,dust-node2:8080,dust-node3:8080
      - DUST_NODE_ID=node2
    volumes:
      - dust-node2-data:/app/data
      - dust-node2-logs:/app/logs
    networks:
      - dust-cluster

  dust-node3:
    build: .
    container_name: dust-node3
    hostname: dust-node3
    ports:
      - "8082:8080"
      - "9092:9090"
    environment:
      - DUST_SYSTEM_HOST=dust-node3
      - DUST_SYSTEM_PORT=8080
      - DUST_CLUSTER_SEEDS=dust-node1:8080,dust-node2:8080,dust-node3:8080
      - DUST_NODE_ID=node3
    volumes:
      - dust-node3-data:/app/data
      - dust-node3-logs:/app/logs
    networks:
      - dust-cluster

volumes:
  dust-node1-data:
  dust-node1-logs:
  dust-node2-data:
  dust-node2-logs:
  dust-node3-data:
  dust-node3-logs:

networks:
  dust-cluster:
    driver: bridge
```

## 监控和运维

### 1. 健康检查端点

```java
@RestController
public class HealthController {
    
    @Autowired
    private ActorSystem actorSystem;
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            // 检查Actor系统状态
            boolean systemHealthy = !actorSystem.isStopped();
            status.put("status", systemHealthy ? "UP" : "DOWN");
            status.put("actorSystem", systemHealthy ? "RUNNING" : "STOPPED");
            
            // 检查内存使用
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            Map<String, Object> memory = new HashMap<>();
            memory.put("max", maxMemory);
            memory.put("total", totalMemory);
            memory.put("used", usedMemory);
            memory.put("free", freeMemory);
            memory.put("usage", (double) usedMemory / maxMemory);
            
            status.put("memory", memory);
            status.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            status.put("status", "DOWN");
            status.put("error", e.getMessage());
            return ResponseEntity.status(503).body(status);
        }
    }
}
```

### 2. 性能监控

```bash
#!/bin/bash
# monitor.sh - 性能监控脚本

APP_NAME="dust-core-app"
METRICS_FILE="/var/log/${APP_NAME}/metrics.log"

while true; do
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
    
    # CPU使用率
    CPU_USAGE=$(top -bn1 | grep "${APP_NAME}" | awk '{print $9}')
    
    # 内存使用
    MEMORY_INFO=$(ps -p $(pgrep -f ${APP_NAME}) -o pid,vsz,rss,pmem --no-headers)
    
    # JVM信息
    JVM_INFO=$(jstat -gc $(pgrep -f ${APP_NAME}) | tail -1)
    
    # 网络连接
    CONNECTIONS=$(netstat -an | grep :8080 | wc -l)
    
    # 记录指标
    echo "${TIMESTAMP},CPU:${CPU_USAGE},MEMORY:${MEMORY_INFO},CONNECTIONS:${CONNECTIONS}" >> ${METRICS_FILE}
    
    sleep 60
done
```

## 故障排除

### 常见问题和解决方案

1. **OutOfMemoryError**
   - 增加堆内存: `-Xmx4g`
   - 检查内存泄漏
   - 优化Actor数量

2. **连接超时**
   - 检查网络配置
   - 调整连接超时参数
   - 验证防火墙设置

3. **Actor死锁**
   - 检查消息循环
   - 避免同步调用
   - 使用超时机制

4. **持久化失败**
   - 检查磁盘空间
   - 验证文件权限
   - 检查序列化兼容性

通过这个部署指南，您可以在各种环境中成功部署和运行Dust-Core应用程序。
